@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('./modal-fixes.css');

/* === CSS VARIABLES === */
:root {
  --base-color: #1b524f;
  --base-color-light: color-mix(in srgb, var(--base-color), white 10%);
  --base-color-lighter: color-mix(in srgb, var(--base-color), white 75%);
  --base-color-lightest: color-mix(in srgb, var(--base-color), white 90%);
  --base-color-dark: color-mix(in srgb, var(--base-color), black 10%);
  --base-color-bright: #2d6b67; /* Brighter version for links and timestamps */
  --fontFamily: "Arial, sans-serif";
  --border-color: #f0f0f0;
  --border-radius: 8px;
}

/* === GLOBAL RESET === */
*,
html,
body,
#root {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body,
#root {
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-family: var(--fontFamily);
  font-weight: 600;
}

/* Global font family and size override for text elements only */
body,
p,
div,
span,
h1, h2, h3, h4, h5, h6,
a,
button,
input,
textarea,
select,
label,
td,
th,
li,
ul,
ol {
  font-family: Arial, sans-serif !important;
  font-size: 14px !important;
}

/* Preserve icon fonts - exclude them from global font override */
.anticon,
.anticon *,
[class*="anticon"],
[class*="anticon"] *,
.fa,
.fa *,
.fas,
.fas *,
.far,
.far *,
.fal,
.fal *,
.fat,
.fat *,
.fad,
.fad *,
.fab,
.fab *,
.fass,
.fass *,
[class*="fa-"],
[class*="fa-"] *,
[class^="fa-"],
[class^="fa-"] *,
i[class*="fa-"],
i[class*="fa-"] *,
i[class^="fa-"],
i[class^="fa-"] *,
.icon,
.icon *,
[class*="icon-"],
[class*="icon-"] * {
  font-family: "Font Awesome 6 Pro", "Font Awesome 6 Free", "FontAwesome", inherit !important;
}

/* Additional FontAwesome specific overrides */
.fa-solid,
.fa-regular,
.fa-light,
.fa-thin,
.fa-duotone,
.fa-brands,
.fa-sharp {
  font-family: "Font Awesome 6 Pro" !important;
  font-size: inherit !important;
}

/* Ensure FontAwesome icons display properly */
i.fa-solid::before,
i.fa-regular::before,
i.fa-light::before,
i.fa-thin::before,
i.fa-duotone::before,
i.fa-brands::before,
i.fa-sharp::before {
  font-family: "Font Awesome 6 Pro" !important;
}

/* Specific font size overrides for UI components */
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  font-size: 14px !important;
}

.ant-btn {
  font-size: 14px !important;
}

.ant-form-item-label > label {
  font-size: 14px !important;
}

.ant-input,
.ant-select-selection-item,
.ant-picker-input > input {
  font-size: 14px !important;
}

.ant-menu-item,
.ant-menu-submenu-title {
  font-size: 14px !important;
}

.ant-pagination-item,
.ant-pagination-prev,
.ant-pagination-next {
  font-size: 14px !important;
}

/* === TIMELINE FIXES === */
/* Fix first timeline item border styling to match other items */
.ant-timeline .ant-timeline-item:first-child .ant-timeline-item-tail {
  border-left: 2px solid #f0f0f0 !important;
  height: 100% !important;
  top: 0 !important;
}

.ant-timeline .ant-timeline-item .ant-timeline-item-tail {
  border-left: 2px solid #f0f0f0 !important;
}

/* Ensure consistent timeline item spacing */
.ant-timeline .ant-timeline-item {
  padding-bottom: 20px !important;
}

.ant-timeline .ant-timeline-item:last-child {
  padding-bottom: 0 !important;
}

/* === LAYOUT PRESERVATION === */
.ant-layout-header {
  background: #fff !important;
}

/* Remove white border/line between sidebar and content */
.ant-layout-sider {
  border-right: none !important;
  border-inline-end: none !important;
  box-shadow: none !important;
}

.ant-layout {
  border: none !important;
}

/* Remove any potential borders from layout components */
.ant-layout-sider-children {
  border: none !important;
}

.ant-layout-content {
  border: none !important;
  border-left: none !important;
  border-inline-start: none !important;
}

.ant-menu {
  background: var(--base-color) !important;
}

/* === MENU ITEM STYLING === */
.ant-menu-item {
  color: white !important;
}

.ant-menu-item a {
  color: white !important;
}

.ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.15) !important;
  color: black !important;
}

.ant-menu-item:hover a {
  color: black !important;
}

.ant-menu-item-selected {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.ant-menu-item-selected a {
  color: white !important;
}

/* For menu items with icons */
.ant-menu-item .anticon {
  color: inherit !important;
}

/* === SCROLLBAR STYLING === */
html *::-webkit-scrollbar {
  width: 12px;
}

html *::-webkit-scrollbar-thumb {
  height: 36px;
  border-radius: var(--border-radius);
  border: 4px solid transparent;
  background-clip: content-box;
  background-color: hsl(0, 0%, 67%);
}

html *::-webkit-scrollbar-track {
  border-radius: 0;
  background-color: transparent;
}

/* === FORM ITEM FLEX ALIGNMENT FIXES === */
/* Fix spacing issues when Form.Item components are used in flex containers */
.ant-flex .ant-form-item {
  margin-bottom: 0 !important;
}

/* For filter forms with multiple flex rows, preserve some spacing between rows */
.ant-flex + .ant-flex .ant-form-item {
  margin-bottom: 0 !important;
}

/* Ensure Form.Item labels and inputs are properly aligned in flex contexts */
.ant-flex .ant-form-item .ant-form-item-label {
  padding-bottom: 0 !important;
}

/* When Form.Item is used in justify-center flex containers, ensure proper alignment */
.ant-flex[style*="justify-content: center"] .ant-form-item,
.ant-flex.ant-flex-justify-center .ant-form-item {
  margin-bottom: 0 !important;
}

/* Also target Ant Design flex components with specific justify classes */
.ant-flex-justify-center .ant-form-item {
  margin-bottom: 0 !important;
}

/* For button containers with mt-4 class (margin-top: 1rem) */
.ant-flex.mt-4 .ant-form-item {
  margin-bottom: 0 !important;
}

/* Special handling for flex containers with gap property */
.ant-flex[style*="gap:"] .ant-form-item,
.ant-flex.ant-flex-gap-middle .ant-form-item,
.ant-flex.ant-flex-gap-small .ant-form-item,
.ant-flex.ant-flex-gap-large .ant-form-item {
  margin-bottom: 0 !important;
}

/* Preserve normal spacing for Form.Item outside of flex containers */
.ant-form .ant-form-item:not(.ant-flex .ant-form-item) {
  margin-bottom: 24px;
}

/* Additional fix for filter forms - ensure form items in multi-row layouts work properly */
.ant-form .ant-flex .ant-form-item {
  align-self: flex-end; /* Align form items to bottom of flex container for consistent baseline */
}

/* === TABLE & PAGINATION OPTIMIZATION === */
@layer utilities {
  /* Disable animations for performance */
  .ant-table-wrapper,
  .ant-table,
  .ant-table-container,
  .ant-table-body,
  table,
  thead,
  tbody,
  tr,
  th,
  td {
    transition: none !important;
    animation: none !important;
    scroll-behavior: auto !important;
  }

  .ant-table-body,
  .ant-table-content {
    -webkit-overflow-scrolling: auto;
  }

  /* Remove table shadows */
  .ant-table-ping-left:not(.ant-table-ping-right)::after,
  .ant-table-ping-right:not(.ant-table-ping-left)::after,
  .ant-table-ping-left.ant-table-ping-right::after,
  .ant-table-ping-left .ant-table-cell-fix-left-last::after,
  .ant-table-ping-right .ant-table-cell-fix-right-first::after {
    display: none !important;
  }

  .ant-table-wrapper .ant-table-container::before,
  .ant-table-wrapper .ant-table-container::after {
    border-radius: var(--border-radius) !important;
    opacity: 0.8;
    pointer-events: none;
    transition: none !important;
  }
}

@layer components {
  /* === GLOBAL TABLE BORDER STYLING === */
  /* Force all table wrappers to have borders globally - this simulates bordered={true} */
  .ant-table-wrapper,
  .global-bordered-table .ant-table-wrapper {
    border: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius) !important;
    overflow: hidden;
  }

  /* Remove gaps between table and pagination */
  .ant-table-wrapper .ant-table {
    margin-bottom: 0 !important;
  }

  .ant-table-wrapper .ant-pagination {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  /* Ensure bordered behavior for all tables */
  .ant-table-wrapper .ant-table-container {
    border: none !important; /* Remove container border since wrapper has it */
    border-radius: 0 !important;
  }

  .ant-table-wrapper .ant-table-content,
  .ant-table-wrapper table {
    border: none !important; /* Remove inner borders since wrapper has border */
    border-radius: 0 !important;
    overflow: hidden;
  }

  .ant-table-wrapper .ant-table {
    border: none !important;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0 !important;
  }
  /* Additional global table border enforcement */
  .ant-table-wrapper:not(.ant-table-wrapper-rtl) {
    border: 1px solid var(--border-color) !important;
  }

  /* Exclude pagination from table cell border rules */
  .ant-table-wrapper .ant-pagination,
  .ant-table-wrapper .ant-pagination * {
    border-right: none !important;
    border-bottom: none !important;
  }
  /* Table headers - green background with white text like sidebar */
  .ant-table-thead > tr > th {
    background-color: var(--base-color) !important;
    border-bottom: 2px solid var(--base-color) !important;
    color: white !important;
    font-weight: 600 !important;
    border-right: 1px solid var(--border-color) !important;
    position: relative !important;
    text-align: left !important;
    padding: 8px 16px !important;
  }

  .ant-table-thead > tr > th:last-child {
    border-right: none !important;
  }

  .ant-table-thead > tr > th:hover {
    background-color: var(--base-color-light) !important;
    color: white !important;
  }

  /* Center the sorters container content */
  .ant-table-thead > tr > th .ant-table-column-sorters {
    justify-content: center !important;
  }

  /* Center the title text and account for sort button space */
  .ant-table-thead > tr > th .ant-table-column-title {
    text-align: center !important;
    margin-right: -14px !important; /* Offset the sort button width to center text */
  }

  /* Ensure sort button is positioned correctly */
  .ant-table-thead > tr > th .ant-table-column-sorter {
    margin-left: 4px !important;
  }

  /* Table body */
  .ant-table-tbody > tr > td {
    border-bottom: 1px solid var(--border-color) !important;
    border-right: 1px solid var(--border-color) !important;
  }

  .ant-table-tbody > tr > td:last-child {
    border-right: none !important;
  }

  .ant-table-tbody > tr:last-child > td {
    border-bottom: none !important;
  }

  /* Remove redundant cell border rules */
  /* Cell borders */
  .ant-table-tbody > tr > td:not(:last-child) {
    border-right: 1px solid var(--border-color) !important;
  }

  /* Row states */
  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: var(--base-color-lightest) !important;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: rgba(27, 82, 79, 0.02) !important;
  }

  /* Remove tooltip from sort buttons - but keep them clickable */

  /* Sorting */
  .ant-table-column-sort {
    background-color: var(--base-color-lighter) !important;
  }

  .ant-table-column-sorter-up.active,
  .ant-table-column-sorter-down.active {
    color: var(--base-color) !important;
  }

  /* Corner radius fixes */
  .ant-table-wrapper .ant-table-container table > thead > tr:first-child > th:first-child {
    border-top-left-radius: var(--border-radius);
  }

  .ant-table-wrapper .ant-table-container table > thead > tr:first-child > th:last-child {
    border-top-right-radius: var(--border-radius);
  }

  .ant-table-wrapper .ant-table-container table > tbody > tr:last-child > td:first-child {
    border-bottom-left-radius: var(--border-radius);
  }

  .ant-table-wrapper .ant-table-container table > tbody > tr:last-child > td:last-child {
    border-bottom-right-radius: var(--border-radius);
  }

  /* === BUTTON STYLING === */
  .ant-btn-primary {
    background-color: var(--base-color) !important;
    border-color: var(--base-color) !important;
    color: white !important;
    box-shadow: none !important;
  }

  .ant-btn-primary:hover,
  .ant-btn-primary:focus {
    background-color: var(--base-color-light) !important;
    border-color: var(--base-color-light) !important;
    color: white !important;
    box-shadow: none !important;
  }

  .ant-btn-primary:active,
  .ant-btn-primary.ant-btn-loading {
    background-color: var(--base-color-dark) !important;
    border-color: var(--base-color-dark) !important;
    color: white !important;
  }


}

/* === TABLE HEADER CENTERING - MOVED TO table-fix.css === */

/* === PAGINATION STYLES === */
.ant-pagination {
  margin: 0 !important;
  padding: 12px 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
  gap: 4px !important;
  padding-right: 16px !important;
}

/* Base pagination items */
.ant-pagination-item,
.ant-pagination-prev,
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  border: 1px solid var(--base-color) !important;
  border-radius: 6px !important;
  min-width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: white !important;
  color: var(--base-color) !important;
  margin: 0 2px !important;
  transition: all 0.2s ease !important;
}

/* Pagination item links */
.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link,
.ant-pagination-jump-prev .ant-pagination-item-link,
.ant-pagination-jump-next .ant-pagination-item-link {
  border: none !important;
  background: none !important;
  color: inherit !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.ant-pagination-item a {
  padding: 6px 8px !important;
  line-height: 1 !important;
  width: 100% !important;
  height: 100% !important;
  color: inherit !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Active and hover states */
.ant-pagination-item-active,
.ant-pagination-item:hover,
.ant-pagination-prev:hover,
.ant-pagination-next:hover,
.ant-pagination-jump-prev:hover,
.ant-pagination-jump-next:hover {
  background-color: var(--base-color) !important;
  color: white !important;
}

.ant-pagination-item-active a,
.ant-pagination-item:hover a,
.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link,
.ant-pagination-jump-prev:hover .ant-pagination-item-link,
.ant-pagination-jump-next:hover .ant-pagination-item-link {
  color: white !important;
}

/* Pagination options */
.ant-pagination-options {
  margin-left: 12px !important;
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
}

.ant-pagination-options .ant-select-selector {
  border: 1px solid var(--base-color) !important;
  border-radius: 6px !important;
  height: 32px !important;
  padding: 0px 24px 0px 8px !important;
  background-color: white !important;
  display: flex !important;
  align-items: center !important;
  line-height: 30px !important;
}

/* Focus and hover states for the selector */
.ant-pagination-options .ant-select:hover .ant-select-selector,
.ant-pagination-options .ant-select-focused .ant-select-selector {
  border-color: var(--base-color) !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.1) !important;
}

/* Remove default focus styles that might interfere */
.ant-pagination-options .ant-select-selector:focus,
.ant-pagination-options .ant-select-selector:focus-within {
  outline: none !important;
  border-color: var(--base-color) !important;
}

.ant-pagination-options .ant-select-selection-item,
.ant-pagination-options .ant-select-arrow,
.ant-pagination-options-quick-jumper,
.ant-pagination-total-text {
  color: var(--base-color) !important;
}

/* Center the selection text and arrow */
.ant-pagination-options .ant-select-selection-item {
  line-height: 30px !important;
  padding: 0 !important;
}

.ant-pagination-options .ant-select-arrow {
  top: 50% !important;
  transform: translateY(-50%) !important;
  right: 8px !important;
  font-size: 12px !important;
}

/* Fix pagination select dropdown panel */
.ant-pagination-options .ant-select-dropdown {
  border: 1px solid var(--base-color) !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(27, 82, 79, 0.15) !important;
}

.ant-pagination-options .ant-select-item {
  color: var(--base-color) !important;
  border-radius: 4px !important;
  margin: 2px !important;
}

.ant-pagination-options .ant-select-item-option-selected {
  background-color: var(--base-color-lightest) !important;
  color: var(--base-color) !important;
}

.ant-pagination-options .ant-select-item-option-active {
  background-color: var(--base-color-lighter) !important;
  color: var(--base-color) !important;
}

/* Hover states for dropdown options */
.ant-pagination-options .ant-select-item:hover {
  background-color: var(--base-color-lighter) !important;
  color: var(--base-color) !important;
}

/* Global dropdown styling */
.ant-select-dropdown {
  border: 1px solid var(--border-color) !important;
}

/* Dropdown option styling - consolidated selectors */
.ant-select-item-option-selected {
  background-color: var(--base-color) !important;
  color: white !important;
}

.ant-select-item-option:hover,
.ant-select-item-option-active {
  background-color: var(--base-color-lightest) !important;
  color: var(--base-color) !important;
}

.ant-pagination-options-quick-jumper {
  margin-left: 12px !important;
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
}

.ant-pagination-options-quick-jumper input {
  border: 1px solid var(--base-color) !important;
  border-radius: 6px !important;
  height: 32px !important;
  width: 50px !important;
  text-align: center !important;
  margin: 0 4px !important;
  color: var(--base-color) !important;
  background-color: white !important;
}

.ant-pagination-total-text {
  margin-right: 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  order: -1 !important;
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
  line-height: 32px !important;
}

/* Disabled states */
.ant-pagination-disabled,
.ant-pagination-disabled .ant-pagination-item-link {
  opacity: 0.4 !important;
  cursor: not-allowed !important;
}

.ant-pagination-disabled:hover,
.ant-pagination-disabled:hover .ant-pagination-item-link {
  background-color: white !important;
  color: var(--base-color) !important;
}

/* Focus states for accessibility */
.ant-pagination-item:focus,
.ant-pagination-prev:focus,
.ant-pagination-next:focus {
  outline: 2px solid var(--base-color) !important;
  outline-offset: 2px !important;
}

/* Override focus states for table pagination - remove outlines */
.ant-table-pagination .ant-pagination-item:focus,
.ant-table-pagination .ant-pagination-prev:focus,
.ant-table-pagination .ant-pagination-next:focus,
.ant-table-pagination .ant-pagination-jump-prev:focus,
.ant-table-pagination .ant-pagination-jump-next:focus {
  outline: none !important;
  outline-offset: 0 !important;
  box-shadow: none !important;
}

/* Table pagination integration - Fix ugly footer pagination */
.ant-table-pagination.ant-pagination,
.ant-table-wrapper + .ant-pagination,
.ant-table-container + .ant-pagination,
.ant-card .ant-table-wrapper + .ant-pagination {
  margin: 0 !important;
  padding: 12px 16px 12px 8px !important;
  justify-content: flex-end !important;
  border-top: 1px solid var(--border-color) !important;
  background-color: #fafafa !important;
  border-bottom-left-radius: var(--border-radius) !important;
  border-bottom-right-radius: var(--border-radius) !important;
}

/* Fix pagination inside table wrapper */
.ant-table-wrapper .ant-table-pagination {
  margin: 0 !important;
  padding: 12px 16px !important;
  background-color: #fafafa !important;
  border-top: 1px solid var(--border-color) !important;
  border-bottom-left-radius: var(--border-radius) !important;
  border-bottom-right-radius: var(--border-radius) !important;
}

/* Ensure pagination items don't get table cell borders */
.ant-table-pagination .ant-pagination-item,
.ant-table-pagination .ant-pagination-prev,
.ant-table-pagination .ant-pagination-next,
.ant-table-pagination .ant-pagination-jump-prev,
.ant-table-pagination .ant-pagination-jump-next {
  border: 1px solid var(--base-color) !important;
  border-radius: 6px !important;
  background-color: white !important;
  margin: 0 2px !important;
  min-width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Fix table pagination item links */
.ant-table-pagination .ant-pagination-item a,
.ant-table-pagination .ant-pagination-prev .ant-pagination-item-link,
.ant-table-pagination .ant-pagination-next .ant-pagination-item-link {
  color: var(--base-color) !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* Active pagination states in table */
.ant-table-pagination .ant-pagination-item-active,
.ant-table-pagination .ant-pagination-item-active a {
  background-color: var(--base-color) !important;
  border-color: var(--base-color) !important;
  color: white !important;
}

/* Fix focus, hover, and click states - remove unwanted borders */
.ant-table-pagination .ant-pagination-item:focus,
.ant-table-pagination .ant-pagination-item:active,
.ant-table-pagination .ant-pagination-item:hover,
.ant-table-pagination .ant-pagination-prev:focus,
.ant-table-pagination .ant-pagination-prev:active,
.ant-table-pagination .ant-pagination-prev:hover,
.ant-table-pagination .ant-pagination-next:focus,
.ant-table-pagination .ant-pagination-next:active,
.ant-table-pagination .ant-pagination-next:hover {
  outline: none !important;
  box-shadow: none !important;
  border: 1px solid var(--base-color) !important;
}

/* Fix pagination item links focus states */
.ant-table-pagination .ant-pagination-item a:focus,
.ant-table-pagination .ant-pagination-item a:active,
.ant-table-pagination .ant-pagination-prev .ant-pagination-item-link:focus,
.ant-table-pagination .ant-pagination-prev .ant-pagination-item-link:active,
.ant-table-pagination .ant-pagination-next .ant-pagination-item-link:focus,
.ant-table-pagination .ant-pagination-next .ant-pagination-item-link:active {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Hover states for pagination items */
.ant-table-pagination .ant-pagination-item:hover,
.ant-table-pagination .ant-pagination-prev:hover,
.ant-table-pagination .ant-pagination-next:hover {
  background-color: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

.ant-table-pagination .ant-pagination-item:hover a,
.ant-table-pagination .ant-pagination-prev:hover .ant-pagination-item-link,
.ant-table-pagination .ant-pagination-next:hover .ant-pagination-item-link {
  color: white !important;
}

/* Remove any global focus rings on pagination */
.ant-table-pagination * {
  outline: none !important;
  box-shadow: none !important;
}

/* Table pagination page size selector styling */
.ant-table-pagination .ant-pagination-options .ant-select-selector {
  border: 1px solid var(--base-color) !important;
  border-radius: 6px !important;
  height: 32px !important;
  background-color: white !important;
  padding: 0px 24px 0px 8px !important;
}

.ant-table-pagination .ant-pagination-options .ant-select:hover .ant-select-selector,
.ant-table-pagination .ant-pagination-options .ant-select-focused .ant-select-selector {
  border-color: var(--base-color) !important;
  box-shadow: 0 0 0 2px rgba(27, 82, 79, 0.1) !important;
}

.ant-table-pagination .ant-pagination-options .ant-select-selection-item {
  color: var(--base-color) !important;
}

.ant-table-pagination .ant-pagination-options .ant-select-arrow {
  color: var(--base-color) !important;
  top: 70% !important;
  transform: translateY(-50%) !important;
  right: 8px !important;
  font-size: 12px !important;
}


