/* Modal styling improvements */
.ant-modal-content {
  padding: 0 !important;
}

.ant-modal-header {
  height: 48px !important; /* Increased height for better visual appearance */
  display: flex !important;
  align-items: center !important;
  padding: 12px 24px !important; /* Increased padding */
  margin-bottom: 12px !important; /* Add more space between header and content */
}

.ant-modal-title {
  color: #ffffff !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  line-height: 1.5 !important;
}

.ant-modal-body {
  padding: 8px 24px 24px !important; /* Increased horizontal padding and bottom padding */
}

/* Better spacing for modals with forms */
.ant-modal-body .ant-form {
  padding-top: 8px !important;
}

.ant-modal-close {
  position: absolute !important;
  top: 12px !important; /* Use fixed positioning instead of percentage */
  right: 16px !important;
  color: #ffffff !important;
  z-index: 10 !important; /* Ensure it's above other elements */
  width: 22px !important; /* Give it explicit dimensions */
  height: 22px !important;
  display: block !important; /* Make sure it's visible */
  opacity: 1 !important; /* Make sure it's visible */
  visibility: visible !important; /* Make sure it's visible */
  pointer-events: auto !important; /* Ensure it's clickable */
}

/* Make sure the close icon is properly centered */
.ant-modal-close-x {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  font-size: 16px !important;
}

/* Specific styling for form modals */
.ant-modal-root .ant-modal-wrap .ant-modal:has(.ant-form) .ant-modal-body {
  padding-top: 12px !important;
}

/* Form field spacing for better layout */
.ant-form .ant-row {
  margin-bottom: 0px !important;
  padding: 6px 0 !important;
}

.ant-form .ant-form-item {
  margin-bottom: 0px !important;
}

/* Improve form modal spacing */
.ant-modal-body .ant-space {
  width: 100% !important;
}

/* Global modal centering - true center positioning */
.ant-modal-wrap {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100vh !important;
}

.ant-modal {
  top: 0 !important;
  transform: none !important;
  padding-bottom: 0 !important;
  margin: 0 !important;
}

.ant-modal-body .ant-radio-group {
  margin-bottom: 16px !important;
  display: flex !important;
  gap: 16px !important;
}

/* Consistent button spacing in form footers */
.ant-modal-body .ant-flex.ant-flex-center {
  margin-top: 24px !important;
  gap: 12px !important;
}

/* Table row borders */
.ant-table-tbody > tr > td {
  border-bottom: 1px solid var(--border-color) !important;
  border-right: 1px solid var(--border-color) !important;
}

.ant-table-thead > tr > th {
  border-right: none !important;
}

.ant-table-thead > tr > th:last-child,
.ant-table-tbody > tr > td:last-child {
  border-right: none !important;
}

/* Enhanced table cell styling with vertical lines */
.ant-table table {
  border-collapse: separate;
  border-spacing: 0;
}

/* Force vertical borders to be visible */
.ant-table-container table {
  border-collapse: separate;
  border-spacing: 0;
}

/* Ensure vertical lines are properly displayed in all browsers */
.ant-table-cell {
  position: relative;
}

.ant-table-cell::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 1px;
  background-color: var(--border-color);
  z-index: 1;
}

.ant-table-cell:last-child::after {
  display: none;
}

/* Table header column borders - subtle dividers */
.ant-table-thead > tr > th::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 1px;
  background-color: var(--border-color);
  z-index: 1;
}

.ant-table-thead > tr > th:last-child::after {
  display: none;
}

/* Pagination right alignment with 16px right margin */
.ant-pagination.ant-table-pagination {
  display: flex !important;
  justify-content: flex-end !important; /* Right align pagination */
  width: 100% !important;
  margin: 16px 0 !important;
  padding: 8px 16px 8px 8px !important;
}

/* Ensure pagination container is right-aligned */
.ant-table-wrapper .ant-pagination.ant-table-pagination {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  padding-right: 16px !important;
}

/* Override any other justification in the main CSS */
.ant-table-pagination.ant-pagination,
.ant-table-wrapper + .ant-pagination {
  justify-content: flex-end !important;
  padding-right: 16px !important;
}

/* Ensure pagination items are properly aligned */
.ant-pagination-item,
.ant-pagination-prev,
.ant-pagination-next,
.ant-pagination-jump-prev,
.ant-pagination-jump-next {
  margin: 0 4px !important; /* Even spacing between items */
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Fix the alignment of pagination item links */
.ant-pagination-item a,
.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* Fix the icons within pagination links */
.ant-pagination-prev .ant-pagination-item-link > *,
.ant-pagination-next .ant-pagination-item-link > * {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Give pagination components better spacing */
.ant-pagination-options {
  margin-left: 16px !important;
  display: inline-flex !important;
  align-items: center !important;
}

/* Ensure the quick jumper and page size selector are aligned */
.ant-pagination-options-quick-jumper,
.ant-pagination-options .ant-select {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
}

/* Fix select item per page icon alignment */
.ant-pagination-options .ant-select-selection-item {
  padding-right: 22px !important; /* Create more space for the arrow icon */
  text-align: left !important; /* Ensure text aligns to the left */
  display: flex !important;
  align-items: center !important; /* Vertically center text */
}

.ant-pagination-options .ant-select-selector {
  padding-right: 8px !important; /* Ensure proper padding */
  position: relative !important;
}

.ant-pagination-options .ant-select-arrow {
  right: 8px !important;
  top: 50% !important;
  margin-top: -5px !important; /* Vertically center arrow */
  pointer-events: none !important; /* Prevents arrow from interfering with clicks */
}

/* Fix previous 5 page and next 5 page icons */
.ant-pagination-jump-prev .ant-pagination-item-container,
.ant-pagination-jump-next .ant-pagination-item-container {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 1 !important; /* Ensure icon appears above ellipsis */
}

/* Fix Quick Jumper input alignment */
.ant-pagination-options-quick-jumper {
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
}

.ant-pagination-options-quick-jumper input {
  margin: 0 8px !important;
  text-align: center !important;
  padding: 4px 8px !important;
}

/* Ensure total count text is properly aligned */
.ant-pagination-total-text {
  margin-right: 16px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
}
