@import url('./font-awesome-6.4.2-pro-main/css/all.css');

// Prevent page scrolling - only allow table scrolling
html, body {
  height: 100%;
  overflow: hidden;
}

#root {
  height: 100%;
  overflow: hidden;
}

.menu-sider {
  .ant-menu-submenu-title {
    color: #ffffff !important;
  }

  .ant-menu-submenu-selected,
  .ant-menu-item-selected,
  .ant-menu-light .ant-menu-item-selected,
  .ant-menu-item-active {
    color: #ffffff !important;
    /* Màu chữ khi được chọn */
  }

  .ant-menu-title-content:hover,
  .ant-menu-submenu-title:hover {
    color: #e2d815 !important;
    /* Màu chữ khi hover */
  }
}

#webpack-dev-server-client-overlay {
  display: none !important;
}

.ant-table-cell {
  padding: 4px 8px !important;
}

.loading-client {
  background-color: #f2f4f7;

  .ant-spin-fullscreen-show {
    opacity: 0.8;
    background-color: #f2f4f7;
  }

  .ant-spin-dot-spin {
    color: #1849a9;
  }

  .ant-spin-text {
    color: black !important;
  }

  .ant-spin-dot {
    font-size: 34px;
  }
}

.ant-modal-content {
  padding: 0 !important;

  .ant-modal-header {
    height: 35px;
    display: flex;
    align-items: center;
  }

  .ant-modal-title {
    color: #ffffff;
    font-size: 16px;
  }

  .ant-modal-body {
    padding: 0 10px 10px;
  }

  .ant-modal-close {
    top: 2px;
    color: #ffffff;
  }
}

.ant-table-wrapper {
  .ant-table-cell-row-hover {
    background-color: #ccebd0 !important;
  }

  .ant-table-thread>tr>th {
    font-weight: 600 !important;
    font-size: 14px;
  }

  .ant-table {
    font-weight: 500;
    font-size: 14px;
  }
}

// Custom drawer styles to fix close button color
.custom-drawer {
  .ant-drawer-close {
    color: #ffffff !important;

    &:hover {
      color: #e2d815 !important;
    }

    .anticon {
      color: #ffffff !important;
    }

    &:hover .anticon {
      color: #e2d815 !important;
    }
  }

  .ant-drawer-header {
    .ant-drawer-close {
      color: #ffffff !important;

      &:hover {
        color: #e2d815 !important;
      }

      .anticon {
        color: #ffffff !important;
      }

      &:hover .anticon {
        color: #e2d815 !important;
      }
    }
  }

  // Target the close icon specifically
  .ant-drawer-close-x {
    color: #ffffff !important;

    &:hover {
      color: #e2d815 !important;
    }
  }

  // Target any SVG icons in the close button
  .ant-drawer-close svg {
    color: #ffffff !important;
    fill: #ffffff !important;
  }

  .ant-drawer-close:hover svg {
    color: #e2d815 !important;
    fill: #e2d815 !important;
  }
}

// Global drawer close button fix (more specific targeting)
.ant-drawer.custom-drawer {
  .ant-drawer-header {
    background-color: #1b524f !important;

    .ant-drawer-close {
      color: #ffffff !important;

      &:hover {
        color: #e2d815 !important;
        background-color: rgba(255, 255, 255, 0.1) !important;
      }

      .anticon,
      .anticon svg,
      span {
        color: #ffffff !important;
        fill: #ffffff !important;
      }

      &:hover .anticon,
      &:hover .anticon svg,
      &:hover span {
        color: #e2d815 !important;
        fill: #e2d815 !important;
      }
    }
  }
}

// Even more specific targeting for drawer close button
.ant-drawer-wrap .ant-drawer.custom-drawer .ant-drawer-content .ant-drawer-header .ant-drawer-close {
  color: #ffffff !important;

  &:hover {
    color: #e2d815 !important;
  }

  .anticon {
    color: #ffffff !important;

    &:hover {
      color: #e2d815 !important;
    }
  }

  svg {
    color: #ffffff !important;
    fill: #ffffff !important;

    &:hover {
      color: #e2d815 !important;
      fill: #e2d815 !important;
    }
  }
}

// Force override any conflicting styles
.custom-drawer .ant-drawer-header .ant-drawer-close * {
  color: #ffffff !important;
}

.custom-drawer .ant-drawer-header .ant-drawer-close:hover * {
  color: #e2d815 !important;
}

// Improve table layout and remove shadows
.ant-card {
  box-shadow: none !important;
  border: 1px solid #f0f0f0 !important;
}

.ant-table-wrapper {
  .ant-table {
    border-radius: 6px;
  }

  .ant-table-container {
    border-radius: 6px;
  }
}

// Ensure proper layout for the main content
.ant-layout-content {
  background: #f5f5f5 !important;
}

// Fix table pagination visibility
.ant-table-wrapper {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;

  .ant-table {
    flex: 1 !important;
    overflow: hidden !important;
    min-height: 0 !important;
    margin-bottom: 0 !important;
  }

  .ant-table-pagination {
    flex-shrink: 0 !important;
    margin: 0 !important;
    padding: 12px 0 !important;
    background: white !important;
    border-top: 1px solid #f0f0f0 !important;
    position: relative !important;
    z-index: 10 !important;
    min-height: 56px !important;
  }

  .ant-table-container {
    flex: 1 !important;
    overflow: hidden !important;
    min-height: 0 !important;
  }

  .ant-table-body {
    overflow-y: auto !important;
    overflow-x: auto !important;
    max-height: calc(100vh - 420px) !important;
  }

  .ant-table-thead > tr > th {
    position: sticky !important;
    top: 0 !important;
    z-index: 5 !important;
    /* Remove conflicting styles - let index.css handle header styling */
  }
}