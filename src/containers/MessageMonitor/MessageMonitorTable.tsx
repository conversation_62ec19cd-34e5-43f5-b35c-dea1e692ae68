import { But<PERSON>, Spin, Table, Tooltip, message } from 'antd';
import { EyeOutlined, RedoOutlined, CopyOutlined } from '@ant-design/icons';
import React, { FC, useMemo, useState } from 'react';
import { MessageMonitorItem } from '@/api/message-monitor';
import { useDashboardStore } from '@/pages/MessageMonitor/Dashboard/useMessageMonitorStore';
import { formatDateTime } from '@/utils/common/common';
import { THEME_COLORS } from '@/utils/ui';

import { useShallow } from 'zustand/react/shallow';
import { StatusTag } from './StatusTag';

// Helper component for truncated text with copy functionality
const TruncatedTextWithCopy: React.FC<{ text: string; maxLength?: number }> = ({
  text,
  maxLength = 20
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(text);
      message.success('Đã sao chép vào clipboard');
    } catch (error) {
      message.error('Không thể sao chép');
    }
  };

  const truncatedText = text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;

  return (
    <div
      style={{
        position: 'relative',
        display: 'inline-flex',
        alignItems: 'center',
        maxWidth: '100%'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <span
        style={{
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '11px',
          color: '#666',
          cursor: 'pointer'
        }}
        title={text}
      >
        {truncatedText}
      </span>
      {isHovered && (
        <Button
          type="text"
          size="small"
          icon={<CopyOutlined />}
          onClick={handleCopy}
          style={{
            marginLeft: '4px',
            padding: '2px 4px',
            height: '18px',
            minWidth: '18px',
            fontSize: '10px'
          }}
        />
      )}
    </div>
  );
};

// Helper component for clickable message ID
const ClickableMessageId: React.FC<{ 
  text: string; 
  maxLength?: number; 
  onClick: () => void;
}> = ({
  text,
  maxLength = 15,
  onClick
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(text);
      message.success('Đã sao chép vào clipboard');
    } catch (error) {
      message.error('Không thể sao chép');
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick();
  };

  const truncatedText = text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;

  return (
    <div
      style={{
        position: 'relative',
        display: 'inline-flex',
        alignItems: 'center',
        maxWidth: '100%'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <span
        style={{
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '11px',
          color: THEME_COLORS.BASE_COLOR_BRIGHT,
          cursor: 'pointer',
          textDecoration: 'underline'
        }}
        title={`Click để xem chi tiết: ${text}`}
        onClick={handleClick}
      >
        {truncatedText}
      </span>
      {isHovered && (
        <Button
          type="text"
          size="small"
          icon={<CopyOutlined />}
          onClick={handleCopy}
          style={{
            marginLeft: '4px',
            padding: '2px 4px',
            height: '18px',
            minWidth: '18px',
            fontSize: '10px'
          }}
        />
      )}
    </div>
  );
};

const getColumns = (
  handleMenuClick: (key: string, record: MessageMonitorItem) => void
) => [
  {
    title: 'Thao tác',
    key: 'action',
    width: 80,
    align: 'center' as const,
    render: (_: any, record: MessageMonitorItem) => (
      <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
        <Tooltip title="Xem chi tiết">
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleMenuClick('view', record)}
            style={{ 
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          />
        </Tooltip>
        <Tooltip title="Gửi lại">
          <Button
            type="text"
            size="small"
            icon={<RedoOutlined />}
            onClick={() => handleMenuClick('resend', record)}
            style={{ 
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          />
        </Tooltip>
      </div>
    ),
  },
  {
    title: 'Mã bản tin',
    dataIndex: 'messageId',
    key: 'messageId',
    width: 150,
    align: 'center' as const,
    render: (value: string, record: MessageMonitorItem) => (
      <ClickableMessageId 
        text={value} 
        maxLength={15} 
        onClick={() => handleMenuClick('view', record)}
      />
    ),
  },
  {
    title: 'Hệ thống gửi',
    dataIndex: 'producerId',
    key: 'producerId',
    width: 120,
    align: 'center' as const,
  },
  {
    title: 'Topic',
    dataIndex: 'topic',
    key: 'topic',
    width: 180,
    align: 'center' as const,
  },
  {
    title: 'Mã nghiệp vụ',
    dataIndex: 'traceId',
    key: 'traceId',
    width: 150,
    align: 'left' as const,
    render: (value: string) => <TruncatedTextWithCopy text={value || ''} maxLength={15} />,
  },
  {
    title: 'Trạng thái gửi',
    dataIndex: 'sentStatus',
    key: 'sentStatus',
    width: 120,
    align: 'center' as const,
    render: (value: number) => <StatusTag status={value} />,
  },
  {
    title: 'Số lần gửi',
    dataIndex: 'retryNum',
    key: 'retryNum',
    width: 100,
    align: 'center' as const,
    render: (value: number) => value || 0,
  },

  {
    title: 'Ngày tạo',
    dataIndex: 'createdTime',
    key: 'createdTime',
    width: 160,
    align: 'center' as const,
    sorter: true,
    render: (value: string | null) => formatDateTime(value),
  },
  {
    title: 'Ngày xử lý cuối',
    dataIndex: 'lastProcessedTime',
    key: 'lastProcessedTime',
    width: 160,
    align: 'center' as const,
    sorter: true,
    render: (value: string | null) => formatDateTime(value),
  },

];

interface MessageMonitorTableProps {
  dataSource?: MessageMonitorItem[];
  total?: number;
}

const MessageMonitorTable: FC<MessageMonitorTableProps> = ({
  dataSource: externalDataSource,
  total: externalTotal
}) => {
  const {
    messageData: storeMessageData,
    pagination: storePagination,
    selectedRowKeys,
    handleTableChange,
    handleMenuClick,
    isFilterLoading,
    setSelectedRowKeys
  } = useDashboardStore(
    useShallow((state: any) => ({
      messageData: state.messageData,
      pagination: state.pagination,
      selectedRowKeys: state.selectedRowKeys,
      handleTableChange: state.handleTableChange,
      handleMenuClick: state.handleMenuClick,
      isFilterLoading: state.isFilterLoading,
      setSelectedRowKeys: state.setSelectedRowKeys,
    })),
  );

  // Use external data if provided, otherwise use store data
  const messageData = externalDataSource || storeMessageData;
  const pagination = externalDataSource ? {
    ...storePagination,
    total: externalTotal || 0
  } : storePagination;

  // Create rowSelection using useMemo to prevent unnecessary recreation
  const rowSelection = useMemo(() => {
    return {
      selectedRowKeys,
      onChange: (newSelectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(newSelectedRowKeys);
      },
    };
  }, [selectedRowKeys, setSelectedRowKeys]);

  // Get columns using the function defined outside the component
  const columns = useMemo(() => getColumns(handleMenuClick), [handleMenuClick]);

  return (
    <div style={{
      position: 'relative'
    }}>
      <Spin spinning={isFilterLoading}>
        <Table
          rowKey="messageId"
          dataSource={messageData}
          columns={columns}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: false,
            showTotal: (total, range) => {
              const selectedCount = selectedRowKeys.length;
              const rangeText = `${range[0]}-${range[1]} của ${total} bản ghi`;
              const selectionText = selectedCount > 0
                ? `Đã chọn ${selectedCount} trong tổng số ${total} bản ghi`
                : rangeText;
              return selectionText;
            },
            position: ['bottomLeft']
          }}
          rowSelection={rowSelection}
          onChange={handleTableChange}
          showSorterTooltip={false}
          scroll={{
            x: 'max-content',
            y: 'calc(100vh - 600px)'
          }}
          style={{
            width: '100%'
          }}
        />
      </Spin>
    </div>
  );
};

export default MessageMonitorTable;
