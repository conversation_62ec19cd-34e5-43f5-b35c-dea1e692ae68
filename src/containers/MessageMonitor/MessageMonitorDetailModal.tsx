import React, { FC, useState } from 'react';
import { Drawer, Button, Space, Spin, Row, Col, Card, Timeline, message, Tooltip, Input, Modal } from 'antd';
import { MessageMonitorItem } from '@/api/message-monitor';
import { useQuery } from '@tanstack/react-query';
import { MessageMonitorApi } from '@/api/message-monitor';
import { formatDateTime } from '@/utils/common/common';
import { THEME_COLORS } from '@/utils/ui';
import { CopyOutlined, EditOutlined, SendOutlined } from '@ant-design/icons';
import { StatusTag } from './StatusTag';

const { TextArea } = Input;

interface MessageMonitorDetailModalProps {
    visible: boolean;
    recordId?: string;
    onClose: () => void;
    onResend: (id: string) => void;
}

// Define the tracking log item interface
interface TrackingLogItem {
    id?: string;
    messageId?: string;
    consumerId?: string | null;
    consumerService?: string | null;
    traceId?: string;
    code?: string;
    message?: string;
    errorTrace?: string | null;
    retryNum?: number;
    lastProcessedTime?: string;
    createdTime?: string;
}

// Helper component for copyable field
const CopyableField: React.FC<{
    label: string;
    value: string;
    isCode?: boolean;
    style?: React.CSSProperties;
}> = ({ label, value, isCode = false, style }) => {
    const [isHovered, setIsHovered] = useState(false);

    const handleCopy = async (e: React.MouseEvent) => {
        e.stopPropagation();
        try {
            await navigator.clipboard.writeText(value);
            message.success('Đã sao chép vào clipboard');
        } catch (error) {
            message.error('Không thể sao chép');
        }
    };

    return (
        <div
            style={{
                fontSize: '13px',
                display: 'flex',
                alignItems: 'center',
                gap: label ? '12px' : '0',
                ...style
            }}
        >
            {label && (
                <span style={{
                    color: '#666',
                    fontWeight: 500,
                    minWidth: 'fit-content',
                    flexShrink: 0,
                    lineHeight: '1.5',
                    display: 'flex',
                    alignItems: 'center'
                }}>
                    {label}:
                </span>
            )}
            <div
                style={{
                    position: 'relative',
                    flex: 1,
                    minWidth: 0,
                    display: 'flex',
                    alignItems: 'center'
                }}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
            >
                <span style={{
                    fontWeight: 500,
                    fontFamily: isCode ? 'Monaco, Menlo, "Ubuntu Mono", monospace' : 'inherit',
                    fontSize: isCode ? '11px' : '13px',
                    backgroundColor: isCode ? '#f5f5f5' : 'transparent',
                    padding: isCode ? '6px 24px 6px 8px' : '0 24px 0 0',
                    borderRadius: isCode ? '4px' : '0',
                    border: isCode ? '1px solid #d9d9d9' : 'none',
                    wordBreak: 'break-all',
                    display: 'block',
                    width: '100%',
                    boxSizing: 'border-box',
                    minHeight: isCode ? '24px' : '20px',
                    lineHeight: '1.5',
                    overflow: 'hidden',
                    whiteSpace: 'pre-wrap'
                }}>
                    {value}
                </span>
                <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={handleCopy}
                    style={{
                        position: 'absolute',
                        right: isCode ? '4px' : '0',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        padding: '2px 4px',
                        height: '18px',
                        minWidth: '18px',
                        fontSize: '10px',
                        opacity: isHovered ? 0.6 : 0,
                        transition: 'opacity 0.2s ease',
                        backgroundColor: 'transparent',
                        border: 'none',
                        borderRadius: '0',
                        color: THEME_COLORS.BASE_COLOR,
                        zIndex: 10
                    }}
                />
            </div>
        </div>
    );
};

// Helper function to format JSON string
const formatJsonString = (jsonString: string | null | undefined): string => {
    if (!jsonString) return '';
    
    try {
        const parsed = JSON.parse(jsonString);
        return JSON.stringify(parsed, null, 2);
    } catch (error) {
        // If not valid JSON, return as-is
        return jsonString;
    }
};

// Helper function to render JSON in a nice format
const renderJsonField = (jsonString: string | null | undefined, maxHeight = '150px') => {
    if (!jsonString) return <span></span>;

    try {
        const parsed = JSON.parse(jsonString);
        return (
            <pre style={{
                whiteSpace: 'pre-wrap',
                fontSize: '12px',
                maxHeight: maxHeight,
                overflow: 'auto',
                backgroundColor: '#f5f5f5',
                padding: '8px',
                borderRadius: '4px',
                border: '1px solid #d9d9d9',
                margin: 0,
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
            }}>
                {JSON.stringify(parsed, null, 2)}
            </pre>
        );
    } catch (error) {
        return (
            <span style={{
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                fontSize: '12px',
                color: '#666'
            }}>
                {jsonString}
            </span>
        );
    }
};


// Helper function to render tracking logs as timeline
const renderTrackingLogsTimeline = (trackingLogs: TrackingLogItem[] | string | null) => {
    console.log('renderTrackingLogsTimeline received:', { trackingLogs, type: typeof trackingLogs, isArray: Array.isArray(trackingLogs) });

    if (!trackingLogs) return <div style={{ color: '#999', fontStyle: 'italic' }}>Không có dữ liệu tracking logs</div>;

    let logs: TrackingLogItem[] = [];

    try {
        // Handle both array and string formats
        if (typeof trackingLogs === 'string') {
            logs = JSON.parse(trackingLogs);
        } else if (Array.isArray(trackingLogs)) {
            logs = trackingLogs;
        } else {
            return <div style={{ color: '#999', fontStyle: 'italic' }}>Không có dữ liệu tracking logs</div>;
        }

        if (!Array.isArray(logs) || logs.length === 0) {
            return <div style={{ color: '#999', fontStyle: 'italic', padding: '16px', textAlign: 'center' }}>Không có dữ liệu lịch sử xử lý</div>;
        }

    } catch (error) {
        console.error('Error parsing tracking logs:', error);
        return <div style={{ color: '#ff4d4f' }}>Lỗi định dạng tracking logs</div>;
    }

    // Sort logs by timestamp (newest first), handle both formats
    const sortedLogs = [...logs].sort((a, b) => {
        const getTimestamp = (log: TrackingLogItem) => {
            // Check if this is current backend format (has timestamp field)
            if ((log as any).timestamp) {
                try {
                    return new Date((log as any).timestamp).getTime();
                } catch (error) {
                    return 0;
                }
            }

            // Your desired TrackingLogItem format
            const timeStr = log.createdTime || log.lastProcessedTime;
            if (!timeStr) return 0;
            try {
                // Handle dd/mm/yyyy hh:mm:ss format
                const [datePart, timePart] = timeStr.split(' ');
                const [day, month, year] = datePart.split('/');
                const dateStr = `${year}-${month}-${day}${timePart ? ' ' + timePart : ''}`;
                return new Date(dateStr).getTime();
            } catch (error) {
                return 0;
            }
        };

        return getTimestamp(b) - getTimestamp(a);
    });

    const timelineItems = sortedLogs.map((log, index) => {
        // Handle both current backend format and your desired format
        let timeStr = '';
        let message = '';
        let code = '';
        let isSuccess = false;
        let isError = false;
        let errorTrace = '';
        let consumerName = '';
        let consumerCode = '';
        let traceId = '';
        let retryNum = 0;

        // Check if this is the current backend format (has timestamp, event, details)
        if ((log as any).timestamp && (log as any).event) {
            const backendLog = log as any;
            try {
                timeStr = formatDateTime(backendLog.timestamp);
            } catch (error) {
                timeStr = backendLog.timestamp || '';
            }
            message = backendLog.details || 'Không có thông tin';
            code = backendLog.code || '';
            isSuccess = (typeof backendLog.event === 'string' && (backendLog.event.includes('PROCESSED') || backendLog.event.includes('SUCCESS'))) || false;
            isError = (typeof backendLog.event === 'string' && (backendLog.event.includes('ERROR') || backendLog.event.includes('FAILED'))) || false;
            errorTrace = backendLog.errorTrace || '';
            consumerName = backendLog.consumerService || '';
            consumerCode = backendLog.consumerId || '';
            traceId = backendLog.traceId || '';
            retryNum = backendLog.retryNum || 0;
        } else {
            // Your desired TrackingLogItem format
            code = log.code || '';
            message = log.message || 'Không có thông tin';
            
            // Format time string using standard format
            const rawTimeStr = log.createdTime || log.lastProcessedTime || '';
            timeStr = formatDateTime(rawTimeStr);
            
            // Success codes: 0, 1, 200 (as strings)
            isSuccess = code === '0' || code === '1' || code === '200';
            isError = log.errorTrace !== null && log.errorTrace !== undefined && log.errorTrace !== '';
            errorTrace = log.errorTrace || '';
            consumerName = log.consumerService || '';
            consumerCode = log.consumerId || '';
            traceId = log.traceId || '';
            retryNum = log.retryNum || 0;
        }

        // Icon logic: Green circle with white dot for success, red circle with X for error
        const iconElement = isSuccess ? (
            <div style={{
                width: '16px',
                height: '16px',
                borderRadius: '50%',
                backgroundColor: '#52c41a',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
            }}>
                <div style={{
                    width: '6px',
                    height: '6px',
                    borderRadius: '50%',
                    backgroundColor: 'white'
                }} />
            </div>
        ) : (
            <div style={{
                width: '16px',
                height: '16px',
                borderRadius: '50%',
                backgroundColor: '#ff4d4f',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '10px',
                fontWeight: 'bold'
            }}>
                ✕
            </div>
        );

        return {
            dot: iconElement,
            children: (
                <div style={{ marginLeft: '4px', paddingBottom: '24px' }}>
                    {/* Timeline Title: Time with Success/Failure Tag - Vertically centered with icon */}
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        marginBottom: '6px',
                        marginTop: '-2px' // Slight adjustment to align with icon center
                    }}>
                        <span style={{
                            fontSize: '14px',
                            fontWeight: 600,
                            color: THEME_COLORS.BASE_COLOR_BRIGHT
                        }}>
                            {timeStr || 'Không có thông tin thời gian'}
                        </span>
                        <span style={{
                            fontSize: '11px',
                            fontWeight: 500,
                            color: 'white',
                            backgroundColor: isSuccess ? '#52c41a' : '#ff4d4f',
                            padding: '2px 6px',
                            borderRadius: '4px'
                        }}>
                            {isSuccess ? 'Thành công' : 'Thất bại'}
                        </span>
                    </div>

                    {/* Details */}
                    <div style={{ fontSize: '13px', lineHeight: '1.5' }}>
                        {/* ID */}
                        {log.id && (
                            <div style={{ marginBottom: '4px' }}>
                                <span style={{ color: '#666', fontWeight: 500 }}>ID: </span>
                                <span style={{
                                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                    fontSize: '12px'
                                }}>
                                    {log.id}
                                </span>
                            </div>
                        )}

                        {/* Kết quả (changed from Nội dung) */}
                        {message && (
                            <div style={{ marginBottom: '4px' }}>
                                <span style={{ color: '#666', fontWeight: 500 }}>Kết quả: </span>
                                <span>{message}</span>
                            </div>
                        )}

                        {/* Hệ thống nhận tin */}
                        {(consumerName || consumerCode) && (
                            <div style={{ marginBottom: '4px' }}>
                                <span style={{ color: '#666', fontWeight: 500 }}>Hệ thống nhận tin: </span>
                                <span>
                                    {consumerName && consumerCode ? `${consumerName} (${consumerCode})` : 
                                     consumerName || consumerCode || 'Không có thông tin'}
                                </span>
                            </div>
                        )}

                        {/* Mã nghiệp vụ */}
                        {traceId && (
                            <div style={{ marginBottom: '4px' }}>
                                <span style={{ color: '#666', fontWeight: 500 }}>Mã nghiệp vụ: </span>
                                <span style={{
                                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                    fontSize: '12px'
                                }}>
                                    {traceId}
                                </span>
                            </div>
                        )}

                        {/* Mã kết quả */}
                        {code && (
                            <div style={{ marginBottom: '4px' }}>
                                <span style={{ color: '#666', fontWeight: 500 }}>Mã kết quả: </span>
                                <span style={{
                                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                    fontSize: '12px',
                                    color: isSuccess ? '#52c41a' : '#ff4d4f',
                                    fontWeight: 500
                                }}>
                                    {code}
                                </span>
                            </div>
                        )}

                        {/* Chi tiết lỗi (chỉ hiện khi có lỗi) */}
                        {isError && errorTrace && (
                            <div style={{ marginBottom: '4px' }}>
                                <span style={{ color: '#666', fontWeight: 500 }}>Chi tiết lỗi: </span>
                                <Button
                                    type="link"
                                    size="small"
                                    style={{ 
                                        padding: '0',
                                        height: 'auto',
                                        color: '#ff4d4f',
                                        textDecoration: 'underline'
                                    }}
                                    onClick={() => {
                                        // Show error detail in modal
                                        const modal = require('antd').Modal;
                                        modal.error({
                                            title: 'Chi tiết lỗi',
                                            content: (
                                                <div style={{
                                                    maxHeight: '400px',
                                                    overflow: 'auto',
                                                    whiteSpace: 'pre-wrap',
                                                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                                    fontSize: '12px',
                                                    backgroundColor: '#f5f5f5',
                                                    padding: '12px',
                                                    borderRadius: '4px',
                                                    border: '1px solid #d9d9d9'
                                                }}>
                                                    {errorTrace}
                                                </div>
                                            ),
                                            width: 800,
                                        });
                                    }}
                                >
                                    Xem chi tiết
                                </Button>
                            </div>
                        )}

                        {/* Số lần xử lý */}
                        <div style={{ marginBottom: '4px' }}>
                            <span style={{ color: '#666', fontWeight: 500 }}>Số lần xử lý: </span>
                            <span style={{ fontWeight: 500 }}>
                                {retryNum || 1}
                            </span>
                        </div>
                    </div>
                </div>
            )
        };
    });

    return (
        <Timeline
            mode="left"
            items={timelineItems}
            style={{
                marginTop: '8px',
                marginLeft: '12px', // Reduced left margin for closer alignment
                paddingLeft: '4px'  // Reduced padding for tighter spacing
            }}
        />
    );
};

const MessageMonitorDetailModal: FC<MessageMonitorDetailModalProps> = ({
    visible,
    recordId,
    onClose,
    onResend,
}) => {
    // State for resend modal and editing
    const [isResendModalVisible, setIsResendModalVisible] = useState(false);
    const [isResending, setIsResending] = useState(false);
    const [editableHeaders, setEditableHeaders] = useState('');
    const [editablePayload, setEditablePayload] = useState('');
    const [hasModifications, setHasModifications] = useState(false);

    // Fetch detail data using React Query
    const { data: detailData, isLoading, error, refetch } = useQuery({
        queryKey: ['messageMonitorDetail', recordId],
        queryFn: () => recordId ? MessageMonitorApi.getById(recordId) : Promise.resolve(null),
        enabled: visible && !!recordId,
        staleTime: 0, // Always fetch fresh data
    });

    const record = detailData?.data;

    // Initialize editable fields when record changes
    React.useEffect(() => {
        if (record) {
            // Format JSON when initializing
            setEditableHeaders(formatJsonString(record.headers));
            setEditablePayload(formatJsonString(record.payload));
            setHasModifications(false);
        }
    }, [record]);

    // Check for modifications - compare JSON content, not string format
    React.useEffect(() => {
        if (record) {
            const isHeadersChanged = () => {
                try {
                    // Parse both original and edited to compare actual content
                    const originalHeaders = JSON.parse(record.headers || '{}');
                    const editedHeaders = JSON.parse(editableHeaders || '{}');
                    return JSON.stringify(originalHeaders) !== JSON.stringify(editedHeaders);
                } catch (error) {
                    // If parsing fails, compare as strings
                    return editableHeaders !== (record.headers || '');
                }
            };

            const isPayloadChanged = () => {
                try {
                    // Parse both original and edited to compare actual content
                    const originalPayload = JSON.parse(record.payload || '{}');
                    const editedPayload = JSON.parse(editablePayload || '{}');
                    return JSON.stringify(originalPayload) !== JSON.stringify(editedPayload);
                } catch (error) {
                    // If parsing fails, compare as strings
                    return editablePayload !== (record.payload || '');
                }
            };

            const headersChanged = isHeadersChanged();
            const payloadChanged = isPayloadChanged();
            setHasModifications(headersChanged || payloadChanged);
        }
    }, [editableHeaders, editablePayload, record]);

    // Log the received data to check trackingLogs format
    if (record) {
        console.log('MessageMonitorDetailModal received record:', {
            messageId: record.messageId,
            trackingLogs: record.trackingLogs,
            trackingLogsType: typeof record.trackingLogs,
            trackingLogsIsArray: Array.isArray(record.trackingLogs)
        });
    }

    // Handle copy to clipboard
    const handleCopy = async (text: string, label: string) => {
        try {
            await navigator.clipboard.writeText(text);
            message.success(`Đã sao chép ${label} vào clipboard`);
        } catch (error) {
            message.error(`Không thể sao chép ${label}`);
        }
    };

    // Handle resend with API replay - now supports custom headers, payload, and key
    const handleCustomResend = async () => {
        if (!recordId) return;

        setIsResending(true);
        try {
            // Pass the editable values to the API
            await MessageMonitorApi.resendMessage(recordId, editableHeaders, editablePayload, record?.key);
            message.success('Gửi lại bản tin thành công');
            setIsResendModalVisible(false);
            // Refresh data to show updated tracking logs
            refetch();
            // Call original onResend callback
            onResend(recordId);
        } catch (error: any) {
            console.error('Error resending message:', error);
            const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi gửi lại bản tin';
            message.error(errorMessage);
        } finally {
            setIsResending(false);
        }
    };

    // Handle simple resend (opens confirmation modal)
    const handleSimpleResend = () => {
        setIsResendModalVisible(true);
    };

    if (error) {
        console.error('Error loading message detail:', error);
    }

    return (
        <Drawer
            title={
                <div style={{
                    fontSize: '16px',
                    fontWeight: 600,
                    color: '#ffffff',
                    margin: 0,
                    padding: 0,
                    display: 'block'
                }}>
                    Chi tiết bản tin
                </div>
            }
            placement="right"
            open={visible}
            onClose={onClose}
            width={1000}
            styles={{
                header: {
                    backgroundColor: '#1b524f',
                    borderBottom: '1px solid #1b524f',
                    color: '#ffffff',
                    padding: '16px 24px',
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                },
                body: { padding: '0' }
            }}
            closeIcon={
                <span style={{
                    color: '#ffffff',
                    fontSize: '16px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '22px',
                    height: '22px',
                    position: 'relative',
                    zIndex: 10,
                    cursor: 'pointer'
                }}>
                    ✕
                </span>
            }
            className="custom-drawer"
        >
            {isLoading ? (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Spin size="large" />
                </div>
            ) : error ? (
                <div style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
                    <p>Có lỗi xảy ra khi tải dữ liệu. Vui lòng thử lại.</p>
                </div>
            ) : record ? (
                <div style={{ padding: '16px 8px 0 8px' }}>
                    {/* Basic Information and Tracking Logs in same row */}
                    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
                        <Col span={12}>
                            {/* Basic Information Card */}
                            <Card
                                title="Thông tin chung"
                                size="small"
                                style={{ height: '350px', display: 'flex', flexDirection: 'column' }}
                                styles={{
                                    body: {
                                        padding: '12px',
                                        flex: 1,
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'flex-start',
                                        overflow: 'auto'
                                    }
                                }}
                            >
                                <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                                    {/* ID Fields at the top */}
                                    <Row gutter={[16, 8]}>
                                        <Col span={24}>
                                            <CopyableField
                                                label="Mã bản tin"
                                                value={record.messageId || ''}
                                                isCode={false}
                                            />
                                        </Col>
                                    </Row>
                                    <Row gutter={[16, 8]} style={{ marginTop: 16 }}>
                                        <Col span={24}>
                                            <CopyableField
                                                label="Mã nghiệp vụ"
                                                value={record.traceId || ''}
                                                isCode={false}
                                            />
                                        </Col>
                                    </Row>

                                    {/* Basic Information */}
                                    <Row gutter={[16, 8]} style={{ marginTop: 16 }}>
                                        <Col span={24}>
                                            <div style={{ fontSize: '13px' }}>
                                                <span style={{ color: '#666', fontWeight: 500 }}>Phần mềm gửi: </span>
                                                <span style={{ fontWeight: 500 }}>
                                                    {record.producerId}
                                                </span>
                                            </div>
                                        </Col>
                                    </Row>
                                <Row gutter={[16, 8]} style={{ marginTop: 12 }}>
                                    <Col span={24}>
                                        <div style={{ fontSize: '13px' }}>
                                            <span style={{ color: '#666', fontWeight: 500 }}>Topic: </span>
                                            <span style={{ fontWeight: 500 }}>
                                                {record.topic}
                                            </span>
                                        </div>
                                    </Col>
                                </Row>
                                <Row gutter={[16, 8]} style={{ marginTop: 12 }}>
                                    <Col span={24}>
                                        <div style={{ fontSize: '13px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                                            <span style={{ color: '#666', fontWeight: 500 }}>Trạng thái gửi: </span>
                                            <StatusTag status={record.sentStatus} />
                                        </div>
                                    </Col>
                                </Row>
                                <Row gutter={[16, 8]} style={{ marginTop: 12 }}>
                                    <Col span={24}>
                                        <div style={{ fontSize: '13px' }}>
                                            <span style={{ color: '#666', fontWeight: 500 }}>Số lần thử lại: </span>
                                            <span style={{ fontWeight: 500 }}>
                                                {record.retryNum || 0}
                                            </span>
                                        </div>
                                    </Col>
                                </Row>
                                <Row gutter={[16, 8]} style={{ marginTop: 12 }}>
                                    <Col span={24}>
                                        <div style={{ fontSize: '13px' }}>
                                            <span style={{ color: '#666', fontWeight: 500 }}>Ngày tạo: </span>
                                            <span style={{ fontWeight: 500 }}>
                                                {formatDateTime(record.createdTime)}
                                            </span>
                                        </div>
                                    </Col>
                                </Row>
                                <Row gutter={[16, 8]} style={{ marginTop: 12 }}>
                                    <Col span={24}>
                                        <div style={{ fontSize: '13px' }}>
                                            <span style={{ color: '#666', fontWeight: 500 }}>Ngày xử lý cuối: </span>
                                            <span style={{ fontWeight: 500 }}>
                                                {formatDateTime(record.lastProcessedTime)}
                                            </span>
                                        </div>
                                    </Col>
                                </Row>


                                </div>
                            </Card>
                        </Col>
                        <Col span={12}>
                            {/* Tracking Logs Card */}
                            {record.trackingLogs ? (
                                <Card
                                    title="Lịch sử xử lý"
                                    size="small"
                                    style={{ height: '350px', display: 'flex', flexDirection: 'column' }}
                                    styles={{
                                        body: {
                                            padding: '12px',
                                            flex: 1,
                                            overflow: 'hidden'
                                        }
                                    }}
                                >
                                    <div style={{
                                        height: '100%',
                                        overflowY: 'auto',
                                        paddingRight: '8px'
                                    }}>
                                        {renderTrackingLogsTimeline(record.trackingLogs)}
                                    </div>
                                </Card>
                            ) : (
                                <Card
                                    title="Lịch sử xử lý"
                                    size="small"
                                    style={{ height: '350px' }}
                                    styles={{ body: { padding: '12px' } }}
                                >
                                    <div style={{ color: '#999', fontStyle: 'italic', textAlign: 'center', padding: '20px' }}>
                                        Không có dữ liệu lịch sử xử lý
                                    </div>
                                </Card>
                            )}
                        </Col>
                    </Row>

                    {/* Origin Message ID Card (if exists) */}
                    {record.originMessageId && (
                        <Card
                            title="Origin Message ID"
                            size="small"
                            style={{ marginBottom: 16 }}
                            styles={{ body: { padding: '12px' } }}
                        >
                            <div style={{
                                fontSize: '11px',
                                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                backgroundColor: '#f5f5f5',
                                padding: '4px 8px',
                                borderRadius: '4px',
                                border: '1px solid #d9d9d9',
                                wordBreak: 'break-all'
                            }}>
                                {record.originMessageId}
                            </div>
                        </Card>
                    )}

                    {/* Headers - Editable */}
                    <Card
                        title={
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <span>Headers</span>
                                <div style={{ display: 'flex', gap: '8px' }}>
                                    <Button
                                        type="text"
                                        size="small"
                                        icon={<CopyOutlined />}
                                        onClick={() => handleCopy(editableHeaders || '', 'headers')}
                                        style={{
                                            fontSize: '12px',
                                            color: THEME_COLORS.BASE_COLOR
                                        }}
                                    >
                                        Copy
                                    </Button>
                                    <Button
                                        type="text"
                                        size="small"
                                        icon={<EditOutlined />}
                                        onClick={() => {
                                            // Reset to original if no changes
                                            if (!hasModifications) {
                                                setEditableHeaders(record.headers || '');
                                            }
                                        }}
                                        style={{
                                            fontSize: '12px',
                                            color: THEME_COLORS.BASE_COLOR
                                        }}
                                    >
                                        {hasModifications ? 'Edited' : 'Edit'}
                                    </Button>
                                </div>
                            </div>
                        }
                        size="small"
                        style={{ marginBottom: 16 }}
                        styles={{ body: { padding: '12px' } }}
                    >
                        <TextArea
                            value={editableHeaders}
                            onChange={(e) => setEditableHeaders(e.target.value)}
                            placeholder="Nhập headers JSON..."
                            autoSize={{ minRows: 6, maxRows: 12 }}
                            style={{
                                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                fontSize: '12px',
                                backgroundColor: hasModifications && editableHeaders !== (record.headers || '') ? '#fff7e6' : '#f5f5f5'
                            }}
                        />
                        {/* Check for actual content changes, not string format */}
                        {(() => {
                            try {
                                const originalHeaders = JSON.parse(record.headers || '{}');
                                const editedHeaders = JSON.parse(editableHeaders || '{}');
                                const hasHeadersChanged = JSON.stringify(originalHeaders) !== JSON.stringify(editedHeaders);
                                
                                return hasHeadersChanged && (
                                    <div style={{ 
                                        marginTop: '8px', 
                                        fontSize: '11px', 
                                        color: '#fa8c16',
                                        fontStyle: 'italic'
                                    }}>
                                        ⚠️ Headers đã được chỉnh sửa
                                    </div>
                                );
                            } catch (error) {
                                // Fallback to string comparison if JSON parsing fails
                                const hasHeadersChanged = editableHeaders !== (record.headers || '');
                                return hasHeadersChanged && (
                                    <div style={{ 
                                        marginTop: '8px', 
                                        fontSize: '11px', 
                                        color: '#fa8c16',
                                        fontStyle: 'italic'
                                    }}>
                                        ⚠️ Headers đã được chỉnh sửa
                                    </div>
                                );
                            }
                        })()}
                    </Card>

                    {/* Payload - Editable */}
                    <Card
                        title={
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <span>Payload</span>
                                <div style={{ display: 'flex', gap: '8px' }}>
                                    <Button
                                        type="text"
                                        size="small"
                                        icon={<CopyOutlined />}
                                        onClick={() => handleCopy(editablePayload || '', 'payload')}
                                        style={{
                                            fontSize: '12px',
                                            color: THEME_COLORS.BASE_COLOR
                                        }}
                                    >
                                        Copy
                                    </Button>
                                    <Button
                                        type="text"
                                        size="small"
                                        icon={<EditOutlined />}
                                        onClick={() => {
                                            // Reset to original if no changes
                                            if (!hasModifications) {
                                                setEditablePayload(record.payload || '');
                                            }
                                        }}
                                        style={{
                                            fontSize: '12px',
                                            color: THEME_COLORS.BASE_COLOR
                                        }}
                                    >
                                        {hasModifications ? 'Edited' : 'Edit'}
                                    </Button>
                                </div>
                            </div>
                        }
                        size="small"
                        style={{ marginBottom: 16 }}
                        styles={{ body: { padding: '12px' } }}
                    >
                        <TextArea
                            value={editablePayload}
                            onChange={(e) => setEditablePayload(e.target.value)}
                            placeholder="Nhập payload JSON..."
                            autoSize={{ minRows: 6, maxRows: 12 }}
                            style={{
                                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                                fontSize: '12px',
                                backgroundColor: hasModifications && editablePayload !== (record.payload || '') ? '#fff7e6' : '#f5f5f5'
                            }}
                        />
                        {/* Check for actual content changes for payload */}
                        {(() => {
                            try {
                                const originalPayload = JSON.parse(record.payload || '{}');
                                const editedPayload = JSON.parse(editablePayload || '{}');
                                const hasPayloadChanged = JSON.stringify(originalPayload) !== JSON.stringify(editedPayload);
                                
                                return hasPayloadChanged && (
                                    <div style={{ 
                                        marginTop: '8px', 
                                        fontSize: '11px', 
                                        color: '#fa8c16',
                                        fontStyle: 'italic'
                                    }}>
                                        ⚠️ Payload đã được chỉnh sửa
                                    </div>
                                );
                            } catch (error) {
                                // Fallback to string comparison if JSON parsing fails
                                const hasPayloadChanged = editablePayload !== (record.payload || '');
                                return hasPayloadChanged && (
                                    <div style={{ 
                                        marginTop: '8px', 
                                        fontSize: '11px', 
                                        color: '#fa8c16',
                                        fontStyle: 'italic'
                                    }}>
                                        ⚠️ Payload đã được chỉnh sửa
                                    </div>
                                );
                            }
                        })()}
                    </Card>



                    {/* Action Buttons */}
                    <div style={{
                        position: 'sticky',
                        bottom: 0,
                        backgroundColor: 'white',
                        padding: '16px 0',
                        borderTop: '1px solid #f0f0f0',
                        marginTop: 16
                    }}>
                        <Space style={{ width: '100%', justifyContent: 'center' }}>
                            <Button
                                type="primary"
                                onClick={handleSimpleResend}
                                disabled={isLoading || !record}
                                style={{
                                    backgroundColor: THEME_COLORS.BASE_COLOR,
                                    borderColor: THEME_COLORS.BASE_COLOR
                                }}
                            >
                                Gửi lại
                            </Button>
                            <Button
                                onClick={onClose}
                                type="default"
                            >
                                Đóng
                            </Button>
                        </Space>
                    </div>

                    {/* Simple Confirmation Modal with fixed text color */}
                    <Modal
                        title="Xác nhận gửi lại bản tin"
                        visible={isResendModalVisible}
                        onCancel={() => setIsResendModalVisible(false)}
                        footer={null}
                        width={500}
                        destroyOnClose={true}
                        bodyStyle={{ 
                            padding: '24px',
                            color: '#000000 !important' // Force black text color
                        }}
                        className="confirm-resend-modal"
                    >
                        <div style={{ 
                            textAlign: 'center',
                            color: '#000000' // Explicit black color
                        }}>
                            {/* Different messages based on modifications */}
                            {hasModifications ? (
                                <p style={{ 
                                    fontSize: '14px', 
                                    marginBottom: '24px',
                                    color: '#000000 !important' // Force black text
                                }}>
                                    Thông tin bản tin khác so với bản tin gốc, bạn có muốn gửi lại bản tin với thông tin hiện tại không?
                                </p>
                            ) : (
                                <p style={{ 
                                    fontSize: '14px', 
                                    marginBottom: '24px',
                                    color: '#000000 !important' // Force black text
                                }}>
                                    Bạn có chắc chắn gửi lại bản tin này hay không?
                                </p>
                            )}

                            {/* Action Buttons */}
                            <div style={{ 
                                display: 'flex', 
                                justifyContent: 'center', 
                                gap: '12px'
                            }}>
                                <Button
                                    type="primary"
                                    onClick={handleCustomResend}
                                    loading={isResending}
                                    disabled={isResending}
                                    style={{
                                        backgroundColor: THEME_COLORS.BASE_COLOR,
                                        borderColor: THEME_COLORS.BASE_COLOR,
                                        minWidth: '100px',
                                        color: '#ffffff' // Ensure white text on primary button
                                    }}
                                >
                                    {isResending ? 'Đang gửi...' : 'Có'}
                                </Button>
                                <Button
                                    onClick={() => setIsResendModalVisible(false)}
                                    type="default"
                                    disabled={isResending}
                                    style={{ 
                                        minWidth: '80px',
                                        color: '#000000' // Ensure black text on default button
                                    }}
                                >
                                    Không
                                </Button>
                            </div>
                        </div>
                    </Modal>
                </div>
            ) : (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                    <p>Không tìm thấy dữ liệu</p>
                </div>
            )}
        </Drawer>
    );
};

export default MessageMonitorDetailModal;
