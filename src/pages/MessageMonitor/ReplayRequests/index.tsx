import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, message, Modal } from 'antd';
import { ReloadOutlined, EyeOutlined, SearchOutlined } from '@ant-design/icons';
import { MessageMonitorApi, ReplayRequest, SearchReplayRequest } from '@/api/message-monitor';
import { THEME_COLORS } from '@/utils/ui';
import { useProducerOptions } from '@/hooks/useProducerOptions';
import { initializeApiToken } from '@/utils/common/storage';
import { FloatingSelectField } from '@/components/form/FloatingSelectField';
import { GrafanaTimeRangePicker } from '@/components/GrafanaTimeRangePicker';
import { useForm, FormProvider } from 'react-hook-form';
import dayjs from 'dayjs';

const ReplayRequestManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [requests, setRequests] = useState<ReplayRequest[]>([]);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  const [filters, setFilters] = useState<SearchReplayRequest>({});
  const [selectedRequest, setSelectedRequest] = useState<ReplayRequest | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  const { options: producerOptions, loading: producerLoading } = useProducerOptions();

  // Form setup for floating fields
  const methods = useForm<{
    type: string | null;
    appCode: string | null;
    status: string | null;
    dateRange: [Date, Date] | null;
  }>({
    defaultValues: {
      type: null,
      appCode: null,
      status: null,
      dateRange: null
    }
  });

  // Initialize API token on component mount
  useEffect(() => {
    console.log('ReplayRequestManagement: Initializing API token...');
    initializeApiToken();
  }, []);

  // Fetch replay requests
  const fetchRequests = async () => {
    try {
      console.log('ReplayRequestManagement: Starting fetchRequests...');
      setLoading(true);
      
      const params = {
        ...filters,
        page: pagination.current - 1,
        size: pagination.pageSize,
      };

      console.log('ReplayRequestManagement: API params:', params);
      
      const response = await MessageMonitorApi.searchReplayRequests(params);
      
      console.log('ReplayRequestManagement: API response:', response);
      
      if (response.success && response.data) {
        console.log('ReplayRequestManagement: Setting data:', response.data.content?.length || 0, 'items');
        setRequests(response.data.content || []);
        setTotal(response.data.totalElements || 0);
      } else {
        console.error('ReplayRequestManagement: API returned unsuccessful response:', response);
        message.error(response.message || 'Không thể tải dữ liệu');
      }
    } catch (error) {
      console.error('ReplayRequestManagement: Error fetching replay requests:', error);
      
      // More detailed error logging
      if (error instanceof Error) {
        console.error('ReplayRequestManagement: Error message:', error.message);
        console.error('ReplayRequestManagement: Error stack:', error.stack);
      }
      
      message.error('Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    console.log('ReplayRequestManagement: Component mounted, calling fetchRequests...');
    fetchRequests();
  }, []); // Empty dependency array - only run on mount

  // Handle pagination changes separately
  useEffect(() => {
    if (pagination.current > 1 || pagination.pageSize !== 10) {
      console.log('ReplayRequestManagement: Pagination changed, calling fetchRequests...');
      fetchRequests();
    }
  }, [pagination.current, pagination.pageSize]);

  // Handle filters changes
  useEffect(() => {
    if (Object.keys(filters).length > 0) {
      console.log('ReplayRequestManagement: Filters changed, calling fetchRequests...');
      fetchRequests();
    }
  }, [filters]);

  // Handle view detail
  const handleViewDetail = async (request: ReplayRequest) => {
    try {
      const response = await MessageMonitorApi.getReplayRequestById(request.id);
      if (response.success && response.data) {
        setSelectedRequest(response.data);
        setDetailModalVisible(true);
      }
    } catch (error) {
      console.error('Error fetching request detail:', error);
      message.error('Không thể tải chi tiết yêu cầu');
    }
  };

  // Handle table change
  const handleTableChange = (newPagination: any) => {
    setPagination({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  // Handle search
  const handleSearch = () => {
    console.log('handleSearch called');
    const formData = methods.getValues();

    // Convert form data to filters
    const newFilters: SearchReplayRequest = {};
    if (formData.type) newFilters.type = formData.type as 'RANGE' | 'IDS';
    if (formData.appCode) newFilters.appCode = formData.appCode;
    if (formData.status) newFilters.status = formData.status as 'SCHEDULED' | 'RUNNING' | 'COMPLETED' | 'FAILED';
    if (formData.dateRange && Array.isArray(formData.dateRange) && formData.dateRange.length === 2) {
      newFilters.fromDate = formData.dateRange[0].toISOString();
      newFilters.toDate = formData.dateRange[1].toISOString();
    }

    setFilters(newFilters);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // Handle refresh
  const handleRefresh = () => {
    console.log('handleRefresh called');
    fetchRequests();
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'RUNNING':
        return 'processing';
      case 'SCHEDULED':
        return 'default';
      case 'FAILED':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'Hoàn thành';
      case 'RUNNING':
        return 'Đang chạy';
      case 'SCHEDULED':
        return 'Đã lên lịch';
      case 'FAILED':
        return 'Thất bại';
      default:
        return status;
    }
  };

  const columns = [
    {
      title: <div style={{ textAlign: 'center' }}>ID</div>,
      dataIndex: 'id',
      key: 'id',
      width: 200,
      align: 'left' as const,
      render: (text: string) => (
        <span style={{
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '11px'
        }}>
          {text}
        </span>
      ),
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      align: 'center' as const,
      render: (type: string) => (
        <Tag color={type === 'RANGE' ? 'blue' : 'green'}>
          {type === 'RANGE' ? 'Khoảng thời gian' : 'Danh sách ID'}
        </Tag>
      ),
    },
    {
      title: 'Ứng dụng',
      dataIndex: 'appCode',
      key: 'appCode',
      width: 80,
      align: 'center' as const,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center' as const,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: 'Tổng số tin',
      dataIndex: 'totalMessages',
      key: 'totalMessages',
      width: 100,
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
    {
      title: 'Đã xử lý',
      dataIndex: 'processedMessages',
      key: 'processedMessages',
      width: 100,
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
    {
      title: 'Tiến độ',
      key: 'progress',
      width: 100,
      align: 'center' as const,
      render: (_: any, record: ReplayRequest) => {
        const progress = record.totalMessages && record.totalMessages > 0 
          ? Math.round((record.processedMessages || 0) / record.totalMessages * 100)
          : 0;
        return `${progress}%`;
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdTime',
      key: 'createdTime',
      align: 'center' as const,
      width: 160,
      render: (value: string) => value ? dayjs(value).format('DD/MM/YYYY HH:mm:ss') : '-',
    },
    {
      title: 'Cập nhật cuối',
      dataIndex: 'updatedTime',
      key: 'updatedTime',
      align: 'center' as const,
      width: 160,
      render: (value: string) => value ? dayjs(value).format('DD/MM/YYYY HH:mm:ss') : '-',
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 80,
      align: 'center' as const,
      render: (_: any, record: ReplayRequest) => (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Space>
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
              size="small"
              style={{
                color: THEME_COLORS.BASE_COLOR,
              }}
            >
              Chi tiết
            </Button>
          </Space>
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '16px' }}>
      {/* Add CSS for centering tags */}
      <style>
        {`
          .centered-tags-table .ant-table-tbody > tr > td {
            text-align: center !important;
            vertical-align: middle !important;
          }
          .centered-tags-table .ant-tag {
            margin: 0 auto !important;
            display: inline-block !important;
          }
        `}
      </style>

      {/* Table */}
      <Card>
        <FormProvider {...methods}>
          {/* Combined Filter Row */}
          <div style={{ marginBottom: '16px', display: 'flex', alignItems: 'center', gap: '24px', justifyContent: 'space-between' }}>
            {/* Left side - Grafana Time Picker */}
            <div style={{ minWidth: '300px' }}>
              <GrafanaTimeRangePicker
                value={methods.watch('dateRange')}
                onChange={(dates: [Date, Date] | null) => {
                  methods.setValue('dateRange', dates);
                  // Immediately call search when date range changes
                  handleSearch();
                }}
                style={{ minWidth: '300px' }}
              />
            </div>

            {/* Right side - Select Fields and Buttons */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flex: 1, justifyContent: 'flex-end' }}>
              <div style={{ minWidth: '150px' }}>
                <FloatingSelectField
                  name="type"
                  floatingLabel="Loại yêu cầu"
                  options={[
                    { label: 'Khoảng thời gian', value: 'RANGE' },
                    { label: 'Danh sách ID', value: 'IDS' }
                  ]}
                  allowClear
                  formItemProps={{ style: { marginBottom: 0 } }}
                />
              </div>

              <div style={{ minWidth: '150px' }}>
                <FloatingSelectField
                  name="appCode"
                  floatingLabel="Ứng dụng"
                  options={producerOptions}
                  allowClear
                  loading={producerLoading}
                  showSearch
                  formItemProps={{ style: { marginBottom: 0 } }}
                />
              </div>

              <div style={{ minWidth: '120px' }}>
                <FloatingSelectField
                  name="status"
                  floatingLabel="Trạng thái"
                  options={[
                    { label: 'Đã lên lịch', value: 'SCHEDULED' },
                    { label: 'Đang chạy', value: 'RUNNING' },
                    { label: 'Hoàn thành', value: 'COMPLETED' },
                    { label: 'Thất bại', value: 'FAILED' }
                  ]}
                  allowClear
                  formItemProps={{ style: { marginBottom: 0 } }}
                />
              </div>

              <div style={{ display: 'flex', gap: '8px', flexShrink: 0 }}>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                  loading={loading}
                  style={{
                    backgroundColor: THEME_COLORS.BASE_COLOR,
                    borderColor: THEME_COLORS.BASE_COLOR,
                  }}
                >
                  Tìm kiếm
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={loading}
                >
                  Làm mới
                </Button>
              </div>
            </div>
          </div>
        </FormProvider>

        <Table
          columns={columns}
          dataSource={requests}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} yêu cầu`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
          className="centered-tags-table"
        />
      </Card>

      {/* Detail Modal */}
      <Modal
        title="Chi tiết yêu cầu gửi lại"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedRequest(null);
        }}
        footer={
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '12px 16px'
          }}>
            <Button
              key="close"
              onClick={() => {
                setDetailModalVisible(false);
                setSelectedRequest(null);
              }}
            >
              Đóng
            </Button>
          </div>
        }
        width={800}
      >
        {selectedRequest && (
          <div style={{ padding: '8px 0' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                <span style={{
                  color: '#666666',
                  fontSize: '14px',
                  fontWeight: 500,
                  minWidth: '120px',
                  display: 'inline-block'
                }}>
                  ID:
                </span>
                <span style={{
                  color: '#000000',
                  fontSize: '14px',
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                  marginLeft: '8px'
                }}>
                  {selectedRequest.id}
                </span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                <span style={{
                  color: '#666666',
                  fontSize: '14px',
                  fontWeight: 500,
                  minWidth: '120px',
                  display: 'inline-block'
                }}>
                  Loại:
                </span>
                <span style={{
                  color: '#000000',
                  fontSize: '14px',
                  marginLeft: '8px'
                }}>
                  {selectedRequest.type === 'RANGE' ? 'Khoảng thời gian' : 'Danh sách ID'}
                </span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                <span style={{
                  color: '#666666',
                  fontSize: '14px',
                  fontWeight: 500,
                  minWidth: '120px',
                  display: 'inline-block'
                }}>
                  Ứng dụng:
                </span>
                <span style={{
                  color: '#000000',
                  fontSize: '14px',
                  marginLeft: '8px'
                }}>
                  {selectedRequest.appCode}
                </span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                <span style={{
                  color: '#666666',
                  fontSize: '14px',
                  fontWeight: 500,
                  minWidth: '120px',
                  display: 'inline-block'
                }}>
                  Trạng thái:
                </span>
                <div style={{ marginLeft: '8px' }}>
                  <Tag color={getStatusColor(selectedRequest.status)}>
                    {getStatusText(selectedRequest.status)}
                  </Tag>
                </div>
              </div>

              {selectedRequest.description && (
                <div style={{ display: 'flex', alignItems: 'flex-start', minHeight: '24px' }}>
                  <span style={{
                    color: '#666666',
                    fontSize: '14px',
                    fontWeight: 500,
                    minWidth: '120px',
                    display: 'inline-block'
                  }}>
                    Mô tả:
                  </span>
                  <span style={{
                    color: '#000000',
                    fontSize: '14px',
                    marginLeft: '8px',
                    lineHeight: '1.5'
                  }}>
                    {selectedRequest.description}
                  </span>
                </div>
              )}

              {selectedRequest.type === 'RANGE' && (
                <>
                  <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                    <span style={{
                      color: '#666666',
                      fontSize: '14px',
                      fontWeight: 500,
                      minWidth: '120px',
                      display: 'inline-block'
                    }}>
                      Từ ngày:
                    </span>
                    <span style={{
                      color: '#000000',
                      fontSize: '14px',
                      marginLeft: '8px'
                    }}>
                      {selectedRequest.fromDate ? dayjs(selectedRequest.fromDate).format('DD/MM/YYYY HH:mm:ss') : '-'}
                    </span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                    <span style={{
                      color: '#666666',
                      fontSize: '14px',
                      fontWeight: 500,
                      minWidth: '120px',
                      display: 'inline-block'
                    }}>
                      Đến ngày:
                    </span>
                    <span style={{
                      color: '#000000',
                      fontSize: '14px',
                      marginLeft: '8px'
                    }}>
                      {selectedRequest.toDate ? dayjs(selectedRequest.toDate).format('DD/MM/YYYY HH:mm:ss') : '-'}
                    </span>
                  </div>
                </>
              )}

              {selectedRequest.type === 'IDS' && selectedRequest.messageIds && Array.isArray(selectedRequest.messageIds) && selectedRequest.messageIds.length > 0 && (
                <div style={{ display: 'flex', alignItems: 'flex-start', minHeight: '24px' }}>
                  <span style={{
                    color: '#666666',
                    fontSize: '14px',
                    fontWeight: 500,
                    minWidth: '120px',
                    display: 'inline-block'
                  }}>
                    Message IDs ({selectedRequest.messageIds.length}):
                  </span>
                  <div style={{
                    marginLeft: '8px',
                    maxHeight: 200,
                    overflowY: 'auto',
                    backgroundColor: '#f5f5f5',
                    padding: 8,
                    borderRadius: 4,
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                    fontSize: '11px',
                    flex: 1
                  }}>
                    {selectedRequest.messageIds.map((id, index) => (
                      <div key={index}>{id}</div>
                    ))}
                  </div>
                </div>
              )}

              <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                <span style={{
                  color: '#666666',
                  fontSize: '14px',
                  fontWeight: 500,
                  minWidth: '120px',
                  display: 'inline-block'
                }}>
                  Tổng số tin:
                </span>
                <span style={{
                  color: '#000000',
                  fontSize: '14px',
                  marginLeft: '8px'
                }}>
                  {selectedRequest.totalMessages || 0}
                </span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                <span style={{
                  color: '#666666',
                  fontSize: '14px',
                  fontWeight: 500,
                  minWidth: '120px',
                  display: 'inline-block'
                }}>
                  Đã xử lý:
                </span>
                <span style={{
                  color: '#000000',
                  fontSize: '14px',
                  marginLeft: '8px'
                }}>
                  {selectedRequest.processedMessages || 0}
                </span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                <span style={{
                  color: '#666666',
                  fontSize: '14px',
                  fontWeight: 500,
                  minWidth: '120px',
                  display: 'inline-block'
                }}>
                  Người tạo:
                </span>
                <span style={{
                  color: '#000000',
                  fontSize: '14px',
                  marginLeft: '8px'
                }}>
                  {selectedRequest.createdBy || '-'}
                </span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                <span style={{
                  color: '#666666',
                  fontSize: '14px',
                  fontWeight: 500,
                  minWidth: '120px',
                  display: 'inline-block'
                }}>
                  Ngày tạo:
                </span>
                <span style={{
                  color: '#000000',
                  fontSize: '14px',
                  marginLeft: '8px'
                }}>
                  {selectedRequest.createdTime ? dayjs(selectedRequest.createdTime).format('DD/MM/YYYY HH:mm:ss') : '-'}
                </span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', minHeight: '24px' }}>
                <span style={{
                  color: '#666666',
                  fontSize: '14px',
                  fontWeight: 500,
                  minWidth: '120px',
                  display: 'inline-block'
                }}>
                  Cập nhật cuối:
                </span>
                <span style={{
                  color: '#000000',
                  fontSize: '14px',
                  marginLeft: '8px'
                }}>
                  {selectedRequest.updatedTime ? dayjs(selectedRequest.updatedTime).format('DD/MM/YYYY HH:mm:ss') : '-'}
                </span>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ReplayRequestManagement;