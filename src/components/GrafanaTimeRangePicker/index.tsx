import React, { useState, useRef, useEffect } from 'react';
import { Button, Input, DatePicker, Space, Divider } from 'antd';
import { ClockCircleOutlined, SearchOutlined, CalendarOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import './styles.css';

const { RangePicker } = DatePicker;

interface TimeRangeOption {
  key: string;
  label: string;
  value: () => [dayjs.Dayjs, dayjs.Dayjs];
}

interface GrafanaTimeRangePickerProps {
  value?: [Date, Date] | null;
  onChange?: (dates: [Date, Date] | null) => void;
  onApply?: (dates: [Date, Date] | null) => void;
  defaultValue?: string;
  style?: React.CSSProperties;
  className?: string;
}

// Time range options similar to <PERSON>ana (Vietnamese)
const timeRangeOptions: TimeRangeOption[] = [
  { key: 'now-15m', label: '15 phút qua', value: () => [dayjs().subtract(15, 'minute'), dayjs()] },
  { key: 'now-30m', label: '30 phút qua', value: () => [dayjs().subtract(30, 'minute'), dayjs()] },
  { key: 'now-1h', label: '1 giờ qua', value: () => [dayjs().subtract(1, 'hour'), dayjs()] },
  { key: 'now-3h', label: '3 giờ qua', value: () => [dayjs().subtract(3, 'hour'), dayjs()] },
  { key: 'now-6h', label: '6 giờ qua', value: () => [dayjs().subtract(6, 'hour'), dayjs()] },
  { key: 'now-12h', label: '12 giờ qua', value: () => [dayjs().subtract(12, 'hour'), dayjs()] },
  { key: 'now-24h', label: '24 giờ qua', value: () => [dayjs().subtract(24, 'hour'), dayjs()] },
  { key: 'now-7d', label: '7 ngày qua', value: () => [dayjs().subtract(7, 'day'), dayjs()] },
  { key: 'now-30d', label: '30 ngày qua', value: () => [dayjs().subtract(30, 'day'), dayjs()] },
  { key: 'today', label: 'Hôm nay', value: () => [dayjs().startOf('day'), dayjs().endOf('day')] },
  { key: 'yesterday', label: 'Hôm qua', value: () => [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')] },
  { key: 'this-week', label: 'Tuần này', value: () => [dayjs().startOf('week'), dayjs().endOf('week')] },
  { key: 'last-week', label: 'Tuần trước', value: () => [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')] },
  { key: 'this-month', label: 'Tháng này', value: () => [dayjs().startOf('month'), dayjs().endOf('month')] },
  { key: 'last-month', label: 'Tháng trước', value: () => [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] },
];

export const GrafanaTimeRangePicker: React.FC<GrafanaTimeRangePickerProps> = ({
  value,
  onChange,
  onApply,
  defaultValue = 'now-7d',
  style,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedKey, setSelectedKey] = useState(defaultValue);
  const [customRange, setCustomRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [isCustomMode, setIsCustomMode] = useState(false);
  const [fromDate, setFromDate] = useState<dayjs.Dayjs | null>(null);
  const [toDate, setToDate] = useState<dayjs.Dayjs | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Get current display text - prioritize option label over custom dates
  const getCurrentDisplayText = () => {
    // If a predefined option is selected, always show the option label
    const option = timeRangeOptions.find(opt => opt.key === selectedKey);
    if (option && !isCustomMode) {
      return option.label;
    }

    // Only show custom dates if explicitly in custom mode (from date pickers)
    if (isCustomMode && customRange) {
      const fromDate = customRange[0].format('DD/MM/YYYY HH:mm');
      const toDate = customRange[1].format('DD/MM/YYYY HH:mm');
      return `${fromDate} ~ ${toDate}`;
    }

    return option ? option.label : '7 ngày qua';
  };

  // Filter options based on search term
  const filteredOptions = timeRangeOptions.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle option selection
  const handleOptionSelect = (option: TimeRangeOption) => {
    setSelectedKey(option.key);
    setIsCustomMode(false); // Clear custom mode when selecting predefined option
    const [fromDate, toDate] = option.value();

    // Update the individual date states but don't set custom mode
    setFromDate(fromDate);
    setToDate(toDate);
    setCustomRange([fromDate, toDate]);

    const dateRange: [Date, Date] = [fromDate.toDate(), toDate.toDate()];

    if (onChange) {
      onChange(dateRange);
    }

    setIsOpen(false);
  };

  // Handle custom range apply
  const handleCustomApply = () => {
    if (fromDate && toDate) {
      const newRange: [dayjs.Dayjs, dayjs.Dayjs] = [fromDate, toDate];
      setCustomRange(newRange);
      setIsCustomMode(true);

      const dateRange: [Date, Date] = [fromDate.toDate(), toDate.toDate()];

      if (onChange) {
        onChange(dateRange);
      }

      if (onApply) {
        onApply(dateRange);
      }

      setIsOpen(false);
    } else if (customRange) {
      setIsCustomMode(true);
      const dateRange: [Date, Date] = [customRange[0].toDate(), customRange[1].toDate()];

      if (onChange) {
        onChange(dateRange);
      }

      if (onApply) {
        onApply(dateRange);
      }

      setIsOpen(false);
    }
  };

  // Handle from date change with default time 00:00:00
  const handleFromDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      const dateWithTime = date.startOf('day'); // Set to 00:00:00
      setFromDate(dateWithTime);

      // If both dates are selected, update the range
      if (toDate) {
        const newRange: [dayjs.Dayjs, dayjs.Dayjs] = [dateWithTime, toDate];
        setCustomRange(newRange);
        setIsCustomMode(true);
      }
    } else {
      setFromDate(null);
    }
  };

  // Handle to date change with default time 23:59:59
  const handleToDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      const dateWithTime = date.endOf('day'); // Set to 23:59:59
      setToDate(dateWithTime);

      // If both dates are selected, update the range
      if (fromDate) {
        const newRange: [dayjs.Dayjs, dayjs.Dayjs] = [fromDate, dateWithTime];
        setCustomRange(newRange);
        setIsCustomMode(true);
      }
    } else {
      setToDate(null);
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Initialize custom range from value
  useEffect(() => {
    if (value && value.length === 2) {
      const fromDateValue = dayjs(value[0]);
      const toDateValue = dayjs(value[1]);

      setFromDate(fromDateValue);
      setToDate(toDateValue);
      setCustomRange([fromDateValue, toDateValue]);
      setIsCustomMode(true);
    }
  }, [value]);

  return (
    <div className={`grafana-time-picker ${className || ''}`} style={style}>
      <Button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="grafana-time-picker-button"
        style={{
          backgroundColor: '#ffffff',
          borderColor: '#d9d9d9',
          color: '#000000',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-start',
          minWidth: 'fit-content',
          maxWidth: '400px',
          height: '32px',
          padding: '4px 12px',
          ...style
        }}
      >
        <Space>
          <ClockCircleOutlined />
          <span>{getCurrentDisplayText()}</span>
        </Space>
      </Button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="grafana-time-picker-dropdown"
          style={{
            position: 'absolute',
            top: '36px',
            right: '0',
            zIndex: 1050,
            backgroundColor: '#ffffff',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
            width: '600px',
            maxHeight: '500px',
            overflow: 'hidden',
            display: 'flex'
          }}
        >
          {/* Left Panel - Quick Ranges */}
          <div style={{
            width: '300px',
            borderRight: '1px solid #d9d9d9',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Search */}
            <div style={{ padding: '12px' }}>
              <Input
                placeholder="Tìm kiếm khoảng thời gian"
                prefix={<SearchOutlined style={{ color: '#8c8c8c' }} />}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  backgroundColor: '#ffffff',
                  borderColor: '#d9d9d9',
                  color: '#000000'
                }}
                className="grafana-search-input"
              />
            </div>

            {/* Quick Range Options */}
            <div style={{
              flex: 1,
              overflowY: 'auto',
              maxHeight: '400px'
            }}>
              {filteredOptions.map((option) => (
                <div
                  key={option.key}
                  onClick={() => handleOptionSelect(option)}
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    color: selectedKey === option.key && !isCustomMode ? '#1b524f' : '#000000',
                    backgroundColor: selectedKey === option.key && !isCustomMode ? '#e6f7ff' : 'transparent',
                    borderLeft: selectedKey === option.key && !isCustomMode ? '2px solid #1b524f' : '2px solid transparent',
                    fontSize: '13px',
                    transition: 'all 0.2s ease'
                  }}
                  className="grafana-option-item"
                >
                  {option.label}
                </div>
              ))}
            </div>
          </div>

          {/* Right Panel - Absolute Time Range */}
          <div style={{
            width: '300px',
            padding: '12px',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{
              color: '#000000',
              fontSize: '14px',
              fontWeight: 500,
              marginBottom: '12px'
            }}>
              Khoảng thời gian tuyệt đối
            </div>

            <div style={{ marginBottom: '12px' }}>
              <div style={{
                color: '#8c8c8c',
                fontSize: '12px',
                marginBottom: '4px'
              }}>
                Từ
              </div>
              <DatePicker
                value={fromDate}
                onChange={handleFromDateChange}
                placeholder="Chọn ngày bắt đầu"
                format="DD/MM/YYYY HH:mm:ss"
                showTime={{
                  format: 'HH:mm:ss',
                  defaultValue: dayjs().startOf('day') // 00:00:00
                }}
                style={{
                  width: '100%',
                  backgroundColor: '#ffffff',
                  borderColor: '#d9d9d9',
                  color: '#000000',
                  marginBottom: '8px'
                }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <div style={{
                color: '#8c8c8c',
                fontSize: '12px',
                marginBottom: '4px'
              }}>
                Đến
              </div>
              <DatePicker
                value={toDate}
                onChange={handleToDateChange}
                placeholder="Chọn ngày kết thúc"
                format="DD/MM/YYYY HH:mm:ss"
                showTime={{
                  format: 'HH:mm:ss',
                  defaultValue: dayjs().endOf('day') // 23:59:59
                }}
                style={{
                  width: '100%',
                  backgroundColor: '#ffffff',
                  borderColor: '#d9d9d9',
                  color: '#000000'
                }}
              />
            </div>



            <Button
              type="primary"
              onClick={handleCustomApply}
              disabled={!fromDate || !toDate}
              style={{
                backgroundColor: fromDate && toDate ? '#1b524f' : '#d9d9d9',
                borderColor: fromDate && toDate ? '#1b524f' : '#d9d9d9',
                width: '100%',
                color: fromDate && toDate ? '#ffffff' : '#8c8c8c'
              }}
            >
              Áp dụng khoảng thời gian
            </Button>


          </div>
        </div>
      )}
    </div>
  );
};

export default GrafanaTimeRangePicker;
