// Global theme colors used throughout the application
export const THEME_COLORS = {
  // Base color from CSS variables
  BASE_COLOR: '#1b524f',
  BASE_COLOR_LIGHT: '#2d5b57',
  BASE_COLOR_DARK: '#164340',
  BASE_COLOR_BRIGHT: '#2d6b67', // Brighter version for links and timestamps

  // Text colors
  WHITE_TEXT: '#ffffff',
  BLACK_TEXT: '#000000',

  // Success/Action colors
  SUCCESS_COLOR: '#52c41a',

  // Background colors
  HEADER_BG: '#1b524f',
  SIDEBAR_BG: '#1b524f',

  // Hover colors
  HOVER_YELLOW: '#e2d815',
} as const;

// Drawer header styles that match the sidebar theme
export const DRAWER_HEADER_STYLES = {
  title: {
    fontSize: '16px',
    fontWeight: 600,
    color: THEME_COLORS.WHITE_TEXT,
    backgroundColor: THEME_COLORS.BASE_COLOR,
    padding: '12px 16px',
    margin: '-16px -24px -16px -24px',
    borderRadius: '0',
    width: 'calc(100% + 48px)',
    display: 'block'
  },
  header: {
    backgroundColor: THEME_COLORS.BASE_COLOR,
    borderBottom: `1px solid ${THEME_COLORS.BASE_COLOR}`,
    color: THEME_COLORS.WHITE_TEXT,
    padding: '16px 24px'
  }
} as const;
