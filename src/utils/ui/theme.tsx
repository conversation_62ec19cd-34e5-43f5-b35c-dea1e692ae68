// Centralized theme colors for consistency
const COLORS = {
  primary: '#1b524f',
  primaryHover: '#2d5b57',
  primaryActive: '#164340',
  white: '#ffffff',
  border: '#f0f0f0',
  disabled: 'rgba(27, 82, 79, 0.4)',
  lightBg: '#ccebd0',
  hoverBg: 'rgba(27, 82, 79, 0.02)',
  selectedBg: 'rgba(27, 82, 79, 0.08)',
  activeBg: 'rgba(27, 82, 79, 0.1)',
} as const;

export const customTheme = {
  token: {
    fontFamily: "'Roboto', 'Helvetica', 'Arial', sans-serif",
    fontWeight: 600,
    fontSize: 16,
    lineHeight: 1.5,
    borderRadius: 8,
    colorBorder: COLORS.primary,
  },
  components: {
    Table: {
      headerBg: COLORS.lightBg,
      headerColor: COLORS.primary,
      headerSortActiveBg: COLORS.lightBg,
      headerSortHoverBg: COLORS.lightBg,
      rowHoverBg: COLORS.hoverBg,
      rowSelectedBg: COLORS.lightBg,
      bodySortBg: COLORS.lightBg,
      rowSelectedHoverBg: COLORS.selectedBg,
      borderColor: COLORS.border,
      headerBorderRadius: 8,
      headerSplitColor: 'transparent',
      colorPrimary: COLORS.primary,
      borderWidth: 1,
      borderStyle: 'solid',
      cellBorderColor: COLORS.border,
      footerBorderColor: COLORS.border,
      borderRadiusOuter: 8,
      borderRadiusInner: 6,
      colorBorder: COLORS.border,
      colorSplit: 'transparent',
      headerIconColor: COLORS.white,
      headerIconColorActive: COLORS.white,
      headerSortActiveIconColor: COLORS.white,
    },
    Pagination: {
      itemBg: COLORS.white,
      itemActiveBg: COLORS.primary,
      itemActiveColor: COLORS.white,
      itemHoverBg: COLORS.primary,
      itemHoverColor: COLORS.white,
      itemSize: 36,
      borderRadius: 6,
      colorPrimary: COLORS.primary,
      colorPrimaryHover: COLORS.primaryHover,
      colorBorder: COLORS.primary,
      colorText: COLORS.primary,
      colorTextDisabled: COLORS.disabled,
      itemActiveBorderColor: COLORS.primary,
      fontSize: 14,
      itemPadding: '0 12px',
    },
    Button: {
      primaryColor: COLORS.white,
      colorPrimary: COLORS.primary,
      colorPrimaryHover: COLORS.primaryHover,
      colorPrimaryActive: COLORS.primaryActive,
      boxShadow: 'none',
      primaryShadow: 'none',
    },
    Checkbox: {
      colorPrimary: COLORS.primary,
      colorPrimaryHover: COLORS.primaryHover,
      colorPrimaryBorder: COLORS.primary,
    },
    Radio: {
      colorPrimary: COLORS.primary,
      colorPrimaryHover: COLORS.primaryHover,
      colorPrimaryBorder: COLORS.primary,
      buttonBg: COLORS.white,
      buttonCheckedBg: COLORS.primary,
      buttonColor: COLORS.primary,
    },
    Modal: {
      headerBg: COLORS.primary,
      headerColor: COLORS.white,
      paddingHeader: 10,
      paddingFooter: 10,
      // Global modal centering
      centered: true,
    },
    Drawer: {
      colorBgElevated: COLORS.white,
      colorText: '#000000',
      colorIcon: COLORS.white,
      colorIconHover: '#e2d815',
    },
    Select: {
      colorPrimary: COLORS.primary,
      colorPrimaryHover: COLORS.primaryHover,
      colorPrimaryActive: COLORS.primaryActive,
      optionSelectedBg: COLORS.primary,
      optionSelectedColor: COLORS.white,
      optionActiveBg: COLORS.activeBg,
      optionActiveColor: COLORS.primary,
      optionHoverBg: COLORS.activeBg,
      optionHoverColor: COLORS.primary,
    },
    Input: {
      colorPrimary: COLORS.primary,
      colorPrimaryHover: COLORS.primaryHover,
      colorPrimaryActive: COLORS.primaryActive,
    },
    DatePicker: {
      colorPrimary: COLORS.primary,
      colorPrimaryHover: COLORS.primaryHover,
      colorPrimaryActive: COLORS.primaryActive,
    }
  },
};
