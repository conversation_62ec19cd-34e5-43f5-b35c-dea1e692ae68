import React, { lazy, ComponentType } from "react";

import { MenuRoles } from "@/containers/types/global";

//#region MENU
export const dataMenu = [
    {
        id: '7f000001-4586-15ec-8951-86e4147f1231',
        name: '<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> tả<PERSON>',
        code: '018043003001',
        iconCls: 'fa-solid fa-radar',
        component: 'Dashboard',
        leaf: true,
        selectable: true,
        typeMenu: 1,
        url: 'kafka-monitor',
    },
    {
        id: '7f000001-4586-15ec-8951-86e4147f1232',
        name: 'Qu<PERSON>n lý bản tin',
        code: '018043003002',
        iconCls: 'fa-solid fa-envelope-circle-check',
        component: null,
        leaf: false,
        selectable: false,
        typeMenu: 1,
        url: null,
        children: [
            {
                id: '7f000001-4586-15ec-8951-86e4147f1232-1',
                name: '<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> bả<PERSON>',
                code: '018043003002001',
                iconCls: 'fa-solid fa-exchange',
                component: 'MessageDashboard',
                leaf: true,
                selectable: true,
                typeMenu: 1,
                url: 'giam-sat-ban-tin',
            },
            {
                id: '7f000001-4586-15ec-8951-86e4147f1233',
                name: 'Quản lý ứng dụng',
                code: '018043003003',
                iconCls: 'fa-solid fa-server',
                component: 'AppManagement',
                leaf: true,
                selectable: true,
                typeMenu: 1,
                url: 'quan-ly-ung-dung',
            },
            {
                id: '7f000001-4586-15ec-8951-86e4147f1235',
                name: 'Yêu cầu gửi lại',
                code: '018043003005',
                iconCls: 'fa-solid fa-redo',
                component: 'ReplayRequests',
                leaf: true,
                selectable: true,
                typeMenu: 1,
                url: 'yeu-cau-gui-lai',
            }
        ]
    }
];

//#region LIST ROLES
export const listRouters: MenuRoles[] = [
    {
        id: '7f000001-4586-15ec-8951-86e4147f1223',
        name: 'Giám sát truyền tải',
        code: '018043003001',
        component: 'Dashboard',
        typeMenu: 1,
        url: 'kafka-monitor',
    },
    {
        id: '7f000001-4586-15ec-8951-86e4147f1224',
        name: 'Giám sát bản tin',
        code: '018043003002',
        component: 'MessageDashboard',
        typeMenu: 1,
        url: 'giam-sat-ban-tin',
    },
    {
        id: '7f000001-4586-15ec-8951-86e4147f1225',
        name: 'Quản lý ứng dụng',
        code: '018043003003',
        component: 'AppManagement',
        typeMenu: 1,
        url: 'quan-ly-ung-dung',
    },
    {
        id: '7f000001-4586-15ec-8951-86e4147f1227',
        name: 'Yêu cầu gửi lại',
        code: '018043003005',
        component: 'ReplayRequests',
        typeMenu: 1,
        url: 'yeu-cau-gui-lai',
    }
];

//#region COMPONENT MAP
export const componentMap: Record<string, React.LazyExoticComponent<ComponentType<any>>> = {
    Dashboard: lazy(() => import('@/pages/KafkaMonitor/Dashboard')),
    MessageDashboard: lazy(() => import('@/pages/MessageMonitor/Dashboard')),
    AppManagement: lazy(() => import('@/pages/MessageMonitor/AppManagement')),
    ReplayRequests: lazy(() => import('@/pages/MessageMonitor/ReplayRequests'))
};
